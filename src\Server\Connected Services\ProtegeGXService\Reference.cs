﻿//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

namespace ProtegeGXService
{
    using System.Runtime.Serialization;
    
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.2.0-preview1.23462.5")]
    [System.Runtime.Serialization.DataContractAttribute(Name="Logon", Namespace="http://schemas.datacontract.org/2004/07/GXWCF2")]
    public partial class Logon : object
    {
        
        private long LogonTypeField;
        
        private string PasswordField;
        
        private string UserNameField;
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public long LogonType
        {
            get
            {
                return this.LogonTypeField;
            }
            set
            {
                this.LogonTypeField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string Password
        {
            get
            {
                return this.PasswordField;
            }
            set
            {
                this.PasswordField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string UserName
        {
            get
            {
                return this.UserNameField;
            }
            set
            {
                this.UserNameField = value;
            }
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.2.0-preview1.23462.5")]
    [System.Runtime.Serialization.DataContractAttribute(Name="DeviceNames", Namespace="http://schemas.datacontract.org/2004/07/GXWCF2")]
    public partial class DeviceNames : object
    {
        
        private ProtegeGXService.DeviceName[] RecordDataField;
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public ProtegeGXService.DeviceName[] RecordData
        {
            get
            {
                return this.RecordDataField;
            }
            set
            {
                this.RecordDataField = value;
            }
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.2.0-preview1.23462.5")]
    [System.Runtime.Serialization.DataContractAttribute(Name="DeviceName", Namespace="http://schemas.datacontract.org/2004/07/GXWCF2")]
    public partial class DeviceName : object
    {
        
        private long DataField;
        
        private long IDField;
        
        private string Name1Field;
        
        private long ParentField;
        
        private long RecordGroupField;
        
        private string StringDataField;
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public long Data
        {
            get
            {
                return this.DataField;
            }
            set
            {
                this.DataField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public long ID
        {
            get
            {
                return this.IDField;
            }
            set
            {
                this.IDField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string Name1
        {
            get
            {
                return this.Name1Field;
            }
            set
            {
                this.Name1Field = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public long Parent
        {
            get
            {
                return this.ParentField;
            }
            set
            {
                this.ParentField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public long RecordGroup
        {
            get
            {
                return this.RecordGroupField;
            }
            set
            {
                this.RecordGroupField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string StringData
        {
            get
            {
                return this.StringDataField;
            }
            set
            {
                this.StringDataField = value;
            }
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.2.0-preview1.23462.5")]
    [System.Runtime.Serialization.DataContractAttribute(Name="ReportDataCollection", Namespace="http://schemas.datacontract.org/2004/07/GXWCF2")]
    public partial class ReportDataCollection : object
    {
        
        private ProtegeGXService.ReportData[] ReportDataField;
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public ProtegeGXService.ReportData[] ReportData
        {
            get
            {
                return this.ReportDataField;
            }
            set
            {
                this.ReportDataField = value;
            }
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.2.0-preview1.23462.5")]
    [System.Runtime.Serialization.DataContractAttribute(Name="ReportData", Namespace="http://schemas.datacontract.org/2004/07/GXWCF2")]
    public partial class ReportData : object
    {
        
        private System.DateTime AcknowledgedTimeField;
        
        private string DescriptionField;
        
        private long DeviceTypeField;
        
        private long DoorIDField;
        
        private string DoorNameField;
        
        private long EventIDField;
        
        private long EventTypeIDField;
        
        private System.DateTime FieldField;
        
        private System.DateTime LoggedField;
        
        private long RecordIndex1Field;
        
        private long RecordIndex2Field;
        
        private long RecordIndex3Field;
        
        private long RecordIndex4Field;
        
        private long RecordIndex5Field;
        
        private long UserIDField;
        
        private string UserNameField;
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public System.DateTime AcknowledgedTime
        {
            get
            {
                return this.AcknowledgedTimeField;
            }
            set
            {
                this.AcknowledgedTimeField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string Description
        {
            get
            {
                return this.DescriptionField;
            }
            set
            {
                this.DescriptionField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public long DeviceType
        {
            get
            {
                return this.DeviceTypeField;
            }
            set
            {
                this.DeviceTypeField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public long DoorID
        {
            get
            {
                return this.DoorIDField;
            }
            set
            {
                this.DoorIDField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string DoorName
        {
            get
            {
                return this.DoorNameField;
            }
            set
            {
                this.DoorNameField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public long EventID
        {
            get
            {
                return this.EventIDField;
            }
            set
            {
                this.EventIDField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public long EventTypeID
        {
            get
            {
                return this.EventTypeIDField;
            }
            set
            {
                this.EventTypeIDField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public System.DateTime Field
        {
            get
            {
                return this.FieldField;
            }
            set
            {
                this.FieldField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public System.DateTime Logged
        {
            get
            {
                return this.LoggedField;
            }
            set
            {
                this.LoggedField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public long RecordIndex1
        {
            get
            {
                return this.RecordIndex1Field;
            }
            set
            {
                this.RecordIndex1Field = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public long RecordIndex2
        {
            get
            {
                return this.RecordIndex2Field;
            }
            set
            {
                this.RecordIndex2Field = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public long RecordIndex3
        {
            get
            {
                return this.RecordIndex3Field;
            }
            set
            {
                this.RecordIndex3Field = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public long RecordIndex4
        {
            get
            {
                return this.RecordIndex4Field;
            }
            set
            {
                this.RecordIndex4Field = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public long RecordIndex5
        {
            get
            {
                return this.RecordIndex5Field;
            }
            set
            {
                this.RecordIndex5Field = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public long UserID
        {
            get
            {
                return this.UserIDField;
            }
            set
            {
                this.UserIDField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string UserName
        {
            get
            {
                return this.UserNameField;
            }
            set
            {
                this.UserNameField = value;
            }
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.2.0-preview1.23462.5")]
    [System.Runtime.Serialization.DataContractAttribute(Name="LogonData", Namespace="http://schemas.datacontract.org/2004/07/GXSV")]
    public partial class LogonData : object
    {
        
        private long CookieField;
        
        private string Hash64Field;
        
        private string LogonField;
        
        private bool NTField;
        
        private long OperatorIDField;
        
        private string OperatorNameField;
        
        private string OperatorName2Field;
        
        private string PasswordField;
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public long Cookie
        {
            get
            {
                return this.CookieField;
            }
            set
            {
                this.CookieField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string Hash64
        {
            get
            {
                return this.Hash64Field;
            }
            set
            {
                this.Hash64Field = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string Logon
        {
            get
            {
                return this.LogonField;
            }
            set
            {
                this.LogonField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public bool NT
        {
            get
            {
                return this.NTField;
            }
            set
            {
                this.NTField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public long OperatorID
        {
            get
            {
                return this.OperatorIDField;
            }
            set
            {
                this.OperatorIDField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string OperatorName
        {
            get
            {
                return this.OperatorNameField;
            }
            set
            {
                this.OperatorNameField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string OperatorName2
        {
            get
            {
                return this.OperatorName2Field;
            }
            set
            {
                this.OperatorName2Field = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string Password
        {
            get
            {
                return this.PasswordField;
            }
            set
            {
                this.PasswordField = value;
            }
        }
    }
    
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.2.0-preview1.23462.5")]
    [System.Runtime.Serialization.DataContractAttribute(Name="MC_FLOOR_CONTROL", Namespace="http://schemas.datacontract.org/2004/07/GXWCF2")]
    public enum MC_FLOOR_CONTROL : int
    {
        
        [System.Runtime.Serialization.EnumMemberAttribute()]
        ICFLOOR_Deactivate = 0,
        
        [System.Runtime.Serialization.EnumMemberAttribute()]
        ICFLOOR_ActivateTimed = 1,
        
        [System.Runtime.Serialization.EnumMemberAttribute()]
        ICFLOOR_ActivateLatched = 2,
    }
    
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.2.0-preview1.23462.5")]
    [System.Runtime.Serialization.DataContractAttribute(Name="MC_PGM_CONTROL", Namespace="http://schemas.datacontract.org/2004/07/GXWCF2")]
    public enum MC_PGM_CONTROL : int
    {
        
        [System.Runtime.Serialization.EnumMemberAttribute()]
        ICPGM_Deactivate = 0,
        
        [System.Runtime.Serialization.EnumMemberAttribute()]
        ICPGM_Activate = 1,
        
        [System.Runtime.Serialization.EnumMemberAttribute()]
        ICPGM_ActivateTimed = 2,
    }
    
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.2.0-preview1.23462.5")]
    [System.Runtime.Serialization.DataContractAttribute(Name="MC_AREA_CONTROL", Namespace="http://schemas.datacontract.org/2004/07/GXWCF2")]
    public enum MC_AREA_CONTROL : int
    {
        
        [System.Runtime.Serialization.EnumMemberAttribute()]
        ICAREA_DisarmArea = 0,
        
        [System.Runtime.Serialization.EnumMemberAttribute()]
        ICAREA_Disarm24Hour = 1,
        
        [System.Runtime.Serialization.EnumMemberAttribute()]
        ICAREA_DisarmAll = 2,
        
        [System.Runtime.Serialization.EnumMemberAttribute()]
        ICAREA_ArmArea = 3,
        
        [System.Runtime.Serialization.EnumMemberAttribute()]
        ICAREA_ForceArmArea = 4,
        
        [System.Runtime.Serialization.EnumMemberAttribute()]
        ICAREA_ArmAreaInstant = 5,
        
        [System.Runtime.Serialization.EnumMemberAttribute()]
        ICAREA_ForceArmAreaInstant = 6,
        
        [System.Runtime.Serialization.EnumMemberAttribute()]
        ICAREA_WalkTestEnable = 7,
        
        [System.Runtime.Serialization.EnumMemberAttribute()]
        ICAREA_WalkTestDisable = 8,
        
        [System.Runtime.Serialization.EnumMemberAttribute()]
        ICAREA_SilenceAlarm = 9,
        
        [System.Runtime.Serialization.EnumMemberAttribute()]
        ICAREA_ArmAreaStay = 10,
        
        [System.Runtime.Serialization.EnumMemberAttribute()]
        ICAREA_ArmArea24 = 11,
    }
    
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.2.0-preview1.23462.5")]
    [System.Runtime.Serialization.DataContractAttribute(Name="MC_PROGFUNC_CONTROL", Namespace="http://schemas.datacontract.org/2004/07/GXWCF2")]
    public enum MC_PROGFUNC_CONTROL : int
    {
        
        [System.Runtime.Serialization.EnumMemberAttribute()]
        ICPGM_Start = 0,
        
        [System.Runtime.Serialization.EnumMemberAttribute()]
        ICPGM_Stop = 1,
    }
    
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.2.0-preview1.23462.5")]
    [System.Runtime.Serialization.DataContractAttribute(Name="MC_DOOR_CONTROL", Namespace="http://schemas.datacontract.org/2004/07/GXWCF2")]
    public enum MC_DOOR_CONTROL : int
    {
        
        [System.Runtime.Serialization.EnumMemberAttribute()]
        ICDOOR_LockDoor = 0,
        
        [System.Runtime.Serialization.EnumMemberAttribute()]
        ICDOOR_UnlockDoor = 1,
        
        [System.Runtime.Serialization.EnumMemberAttribute()]
        ICDOOR_UnlockDoorLatched = 2,
        
        [System.Runtime.Serialization.EnumMemberAttribute()]
        ICDOOR_DoorLockoutEntry = 3,
        
        [System.Runtime.Serialization.EnumMemberAttribute()]
        ICDOOR_DoorLockoutExit = 4,
        
        [System.Runtime.Serialization.EnumMemberAttribute()]
        ICDOOR_DoorLockoutEntryExit = 5,
        
        [System.Runtime.Serialization.EnumMemberAttribute()]
        ICDOOR_DoorLockoutClear = 6,
    }
    
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.2.0-preview1.23462.5")]
    [System.Runtime.Serialization.DataContractAttribute(Name="MC_ZONE_CONTROL", Namespace="http://schemas.datacontract.org/2004/07/GXWCF2")]
    public enum MC_ZONE_CONTROL : int
    {
        
        [System.Runtime.Serialization.EnumMemberAttribute()]
        ICZONE_RemoveBypass = 0,
        
        [System.Runtime.Serialization.EnumMemberAttribute()]
        ICZONE_BypassUntilNextDisarm = 1,
        
        [System.Runtime.Serialization.EnumMemberAttribute()]
        ICZONE_BypassPermanently = 2,
    }
    
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.2.0-preview1.23462.5")]
    [System.Runtime.Serialization.DataContractAttribute(Name="MC_ZONE_TYPE", Namespace="http://schemas.datacontract.org/2004/07/GXWCF2")]
    public enum MC_ZONE_TYPE : int
    {
        
        [System.Runtime.Serialization.EnumMemberAttribute()]
        ICZONE_Zone = 0,
        
        [System.Runtime.Serialization.EnumMemberAttribute()]
        ICZONE_TroubleZone = 1,
    }
    
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.2.0-preview1.23462.5")]
    [System.Runtime.Serialization.DataContractAttribute(Name="MC_MODULE_COMMAND", Namespace="http://schemas.datacontract.org/2004/07/GXWCF2")]
    public enum MC_MODULE_COMMAND : int
    {
        
        [System.Runtime.Serialization.EnumMemberAttribute()]
        ICMODULE_Update = 0,
        
        [System.Runtime.Serialization.EnumMemberAttribute()]
        ICMODULE_Secure = 1,
        
        [System.Runtime.Serialization.EnumMemberAttribute()]
        ICMODULE_UnSecure = 2,
        
        [System.Runtime.Serialization.EnumMemberAttribute()]
        ICMODULE_UpdateSingle = 3,
        
        [System.Runtime.Serialization.EnumMemberAttribute()]
        ICMODULE_UpdateOffline = 4,
        
        [System.Runtime.Serialization.EnumMemberAttribute()]
        ICMODULE_UpdateSingleUsers = 5,
        
        [System.Runtime.Serialization.EnumMemberAttribute()]
        ICMODULE_RestartModuleInBiosMode = 6,
    }
    
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.2.0-preview1.23462.5")]
    [System.Runtime.Serialization.DataContractAttribute(Name="MC_MODULE_COMMAND_TYPE", Namespace="http://schemas.datacontract.org/2004/07/GXWCF2")]
    public enum MC_MODULE_COMMAND_TYPE : int
    {
        
        [System.Runtime.Serialization.EnumMemberAttribute()]
        ICMODULE_Invalid = 0,
        
        [System.Runtime.Serialization.EnumMemberAttribute()]
        ICMODULE_ControlPanel = 1,
        
        [System.Runtime.Serialization.EnumMemberAttribute()]
        ICMODULE_LCDKeypad = 2,
        
        [System.Runtime.Serialization.EnumMemberAttribute()]
        ICMODULE_ZoneExpander = 3,
        
        [System.Runtime.Serialization.EnumMemberAttribute()]
        ICMODULE_ReaderExpander = 4,
        
        [System.Runtime.Serialization.EnumMemberAttribute()]
        ICMODULE_PGMExpander = 5,
        
        [System.Runtime.Serialization.EnumMemberAttribute()]
        ICMODULE_AnalogExpander = 6,
    }
    
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.2.0-preview1.23462.5")]
    [System.Runtime.Serialization.DataContractAttribute(Name="MC_SERVICE_CONTROL", Namespace="http://schemas.datacontract.org/2004/07/GXWCF2")]
    public enum MC_SERVICE_CONTROL : int
    {
        
        [System.Runtime.Serialization.EnumMemberAttribute()]
        ICSERVICE_Stop = 0,
        
        [System.Runtime.Serialization.EnumMemberAttribute()]
        ICSERVICE_Start = 1,
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.2.0-preview1.23462.5")]
    [System.Runtime.Serialization.DataContractAttribute(Name="CImageData", Namespace="http://schemas.datacontract.org/2004/07/GXSV")]
    public partial class CImageData : object
    {
        
        private ProtegeGXService.ByteCollection BytesField;
        
        private long IDField;
        
        private string NameField;
        
        private string TypeField;
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public ProtegeGXService.ByteCollection Bytes
        {
            get
            {
                return this.BytesField;
            }
            set
            {
                this.BytesField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public long ID
        {
            get
            {
                return this.IDField;
            }
            set
            {
                this.IDField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string Name
        {
            get
            {
                return this.NameField;
            }
            set
            {
                this.NameField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string Type
        {
            get
            {
                return this.TypeField;
            }
            set
            {
                this.TypeField = value;
            }
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.2.0-preview1.23462.5")]
    [System.Runtime.Serialization.CollectionDataContractAttribute(Name="ByteCollection", Namespace="http://schemas.datacontract.org/2004/07/GXSV", ItemName="unsignedByte")]
    public class ByteCollection : System.Collections.Generic.List<byte>
    {
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.2.0-preview1.23462.5")]
    [System.Runtime.Serialization.DataContractAttribute(Name="EventWindowInformation", Namespace="http://schemas.datacontract.org/2004/07/GXSV")]
    public partial class EventWindowInformation : object
    {
        
        private long TotalMostRecentEventIDField;
        
        private long TotalOldestEventIDField;
        
        private long WindowMostRecentEventIDField;
        
        private long WindowOldestEventIDField;
        
        private long WindowSizeField;
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public long TotalMostRecentEventID
        {
            get
            {
                return this.TotalMostRecentEventIDField;
            }
            set
            {
                this.TotalMostRecentEventIDField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public long TotalOldestEventID
        {
            get
            {
                return this.TotalOldestEventIDField;
            }
            set
            {
                this.TotalOldestEventIDField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public long WindowMostRecentEventID
        {
            get
            {
                return this.WindowMostRecentEventIDField;
            }
            set
            {
                this.WindowMostRecentEventIDField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public long WindowOldestEventID
        {
            get
            {
                return this.WindowOldestEventIDField;
            }
            set
            {
                this.WindowOldestEventIDField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public long WindowSize
        {
            get
            {
                return this.WindowSizeField;
            }
            set
            {
                this.WindowSizeField = value;
            }
        }
    }
    
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.2.0-preview1.23462.5")]
    [System.ServiceModel.ServiceContractAttribute(ConfigurationName="ProtegeGXService.IService1")]
    public interface IService1
    {
        
        // CODEGEN: Generating message contract since the operation has multiple return values.
        [System.ServiceModel.OperationContractAttribute(Action="http://tempuri.org/IService1/AddRecord", ReplyAction="http://tempuri.org/IService1/AddRecordResponse")]
        System.Threading.Tasks.Task<ProtegeGXService.AddRecordResponse> AddRecordAsync(ProtegeGXService.AddRecordRequest request);
        
        // CODEGEN: Generating message contract since the operation has multiple return values.
        [System.ServiceModel.OperationContractAttribute(Action="http://tempuri.org/IService1/AddRecordSC", ReplyAction="http://tempuri.org/IService1/AddRecordSCResponse")]
        System.Threading.Tasks.Task<ProtegeGXService.AddRecordSCResponse> AddRecordSCAsync(ProtegeGXService.AddRecordSCRequest request);
        
        // CODEGEN: Generating message contract since the operation has multiple return values.
        [System.ServiceModel.OperationContractAttribute(Action="http://tempuri.org/IService1/UpdateRecord", ReplyAction="http://tempuri.org/IService1/UpdateRecordResponse")]
        System.Threading.Tasks.Task<ProtegeGXService.UpdateRecordResponse> UpdateRecordAsync(ProtegeGXService.UpdateRecordRequest request);
        
        // CODEGEN: Generating message contract since the operation has multiple return values.
        [System.ServiceModel.OperationContractAttribute(Action="http://tempuri.org/IService1/GetRecord", ReplyAction="http://tempuri.org/IService1/GetRecordResponse")]
        System.Threading.Tasks.Task<ProtegeGXService.GetRecordResponse> GetRecordAsync(ProtegeGXService.GetRecordRequest request);
        
        // CODEGEN: Generating message contract since the operation has multiple return values.
        [System.ServiceModel.OperationContractAttribute(Action="http://tempuri.org/IService1/DeleteRecord", ReplyAction="http://tempuri.org/IService1/DeleteRecordResponse")]
        System.Threading.Tasks.Task<ProtegeGXService.DeleteRecordResponse> DeleteRecordAsync(ProtegeGXService.DeleteRecordRequest request);
        
        // CODEGEN: Generating message contract since the operation has multiple return values.
        [System.ServiceModel.OperationContractAttribute(Action="http://tempuri.org/IService1/ListRecords", ReplyAction="http://tempuri.org/IService1/ListRecordsResponse")]
        System.Threading.Tasks.Task<ProtegeGXService.ListRecordsResponse> ListRecordsAsync(ProtegeGXService.ListRecordsRequest request);
        
        // CODEGEN: Generating message contract since the operation has multiple return values.
        [System.ServiceModel.OperationContractAttribute(Action="http://tempuri.org/IService1/EventReportByRecordGroup", ReplyAction="http://tempuri.org/IService1/EventReportByRecordGroupResponse")]
        System.Threading.Tasks.Task<ProtegeGXService.EventReportByRecordGroupResponse> EventReportByRecordGroupAsync(ProtegeGXService.EventReportByRecordGroupRequest request);
        
        // CODEGEN: Generating message contract since the operation has multiple return values.
        [System.ServiceModel.OperationContractAttribute(Action="http://tempuri.org/IService1/EventReportByUser", ReplyAction="http://tempuri.org/IService1/EventReportByUserResponse")]
        System.Threading.Tasks.Task<ProtegeGXService.EventReportByUserResponse> EventReportByUserAsync(ProtegeGXService.EventReportByUserRequest request);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://tempuri.org/IService1/GetCurrentLogon", ReplyAction="http://tempuri.org/IService1/GetCurrentLogonResponse")]
        System.Threading.Tasks.Task<string> GetCurrentLogonAsync();
        
        // CODEGEN: Generating message contract since the operation has multiple return values.
        [System.ServiceModel.OperationContractAttribute(Action="http://tempuri.org/IService1/SetLogon", ReplyAction="http://tempuri.org/IService1/SetLogonResponse")]
        System.Threading.Tasks.Task<ProtegeGXService.SetLogonResponse> SetLogonAsync(ProtegeGXService.SetLogonRequest request);
        
        // CODEGEN: Generating message contract since the operation has multiple return values.
        [System.ServiceModel.OperationContractAttribute(Action="http://tempuri.org/IService1/TestMainDatabaseConnection", ReplyAction="http://tempuri.org/IService1/TestMainDatabaseConnectionResponse")]
        System.Threading.Tasks.Task<ProtegeGXService.TestMainDatabaseConnectionResponse> TestMainDatabaseConnectionAsync(ProtegeGXService.TestMainDatabaseConnectionRequest request);
        
        // CODEGEN: Generating message contract since the operation has multiple return values.
        [System.ServiceModel.OperationContractAttribute(Action="http://tempuri.org/IService1/TestEventDatabaseConnection", ReplyAction="http://tempuri.org/IService1/TestEventDatabaseConnectionResponse")]
        System.Threading.Tasks.Task<ProtegeGXService.TestEventDatabaseConnectionResponse> TestEventDatabaseConnectionAsync(ProtegeGXService.TestEventDatabaseConnectionRequest request);
        
        // CODEGEN: Generating message contract since the operation has multiple return values.
        [System.ServiceModel.OperationContractAttribute(Action="http://tempuri.org/IService1/GetDeviceStatus", ReplyAction="http://tempuri.org/IService1/GetDeviceStatusResponse")]
        System.Threading.Tasks.Task<ProtegeGXService.GetDeviceStatusResponse> GetDeviceStatusAsync(ProtegeGXService.GetDeviceStatusRequest request);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://tempuri.org/IService1/ControlFloor", ReplyAction="http://tempuri.org/IService1/ControlFloorResponse")]
        System.Threading.Tasks.Task<bool> ControlFloorAsync(ProtegeGXService.Logon LD, long nSiteID, long nControllerID, long nElevatorCar, long nFloorIndex, ProtegeGXService.MC_FLOOR_CONTROL Control, long nTime);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://tempuri.org/IService1/ControlPGM", ReplyAction="http://tempuri.org/IService1/ControlPGMResponse")]
        System.Threading.Tasks.Task<bool> ControlPGMAsync(ProtegeGXService.Logon LD, long nSiteID, long nControllerID, long nPGMIndex, ProtegeGXService.MC_PGM_CONTROL Control, long nTime);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://tempuri.org/IService1/ControlArea", ReplyAction="http://tempuri.org/IService1/ControlAreaResponse")]
        System.Threading.Tasks.Task<bool> ControlAreaAsync(ProtegeGXService.Logon LD, long nSiteID, long nControllerID, long nAreaIndex, ProtegeGXService.MC_AREA_CONTROL Control);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://tempuri.org/IService1/ControlProgFunc", ReplyAction="http://tempuri.org/IService1/ControlProgFuncResponse")]
        System.Threading.Tasks.Task<bool> ControlProgFuncAsync(ProtegeGXService.Logon LD, long nSiteID, long nControllerID, long nDoorIndex, ProtegeGXService.MC_PROGFUNC_CONTROL Control);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://tempuri.org/IService1/ControlDoor", ReplyAction="http://tempuri.org/IService1/ControlDoorResponse")]
        System.Threading.Tasks.Task<bool> ControlDoorAsync(ProtegeGXService.Logon LD, long nSiteID, long nControllerID, long nDoorIndex, ProtegeGXService.MC_DOOR_CONTROL Control);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://tempuri.org/IService1/ControlZone", ReplyAction="http://tempuri.org/IService1/ControlZoneResponse")]
        System.Threading.Tasks.Task<bool> ControlZoneAsync(ProtegeGXService.Logon LD, long nSiteID, long nControllerID, long nZoneIndex, ProtegeGXService.MC_ZONE_CONTROL Control, ProtegeGXService.MC_ZONE_TYPE ZoneType);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://tempuri.org/IService1/ModuleCommand", ReplyAction="http://tempuri.org/IService1/ModuleCommandResponse")]
        System.Threading.Tasks.Task<bool> ModuleCommandAsync(ProtegeGXService.Logon LD, long nSiteID, long nControllerID, ProtegeGXService.MC_MODULE_COMMAND Command, ProtegeGXService.MC_MODULE_COMMAND_TYPE Type, long nModuleAddress);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://tempuri.org/IService1/ControlService", ReplyAction="http://tempuri.org/IService1/ControlServiceResponse")]
        System.Threading.Tasks.Task<bool> ControlServiceAsync(ProtegeGXService.Logon LD, long nSiteID, long nControllerID, long nServiceType, long nIndex, ProtegeGXService.MC_SERVICE_CONTROL Control);
        
        // CODEGEN: Generating message contract since the operation has multiple return values.
        [System.ServiceModel.OperationContractAttribute(Action="http://tempuri.org/IService1/SetDateTime", ReplyAction="http://tempuri.org/IService1/SetDateTimeResponse")]
        System.Threading.Tasks.Task<ProtegeGXService.SetDateTimeResponse> SetDateTimeAsync(ProtegeGXService.SetDateTimeRequest request);
        
        // CODEGEN: Generating message contract since the operation has multiple return values.
        [System.ServiceModel.OperationContractAttribute(Action="http://tempuri.org/IService1/InitialiseKabaKey", ReplyAction="http://tempuri.org/IService1/InitialiseKabaKeyResponse")]
        System.Threading.Tasks.Task<ProtegeGXService.InitialiseKabaKeyResponse> InitialiseKabaKeyAsync(ProtegeGXService.InitialiseKabaKeyRequest request);
        
        // CODEGEN: Generating message contract since the operation has multiple return values.
        [System.ServiceModel.OperationContractAttribute(Action="http://tempuri.org/IService1/ControlDevice", ReplyAction="http://tempuri.org/IService1/ControlDeviceResponse")]
        System.Threading.Tasks.Task<ProtegeGXService.ControlDeviceResponse> ControlDeviceAsync(ProtegeGXService.ControlDeviceRequest request);
        
        // CODEGEN: Generating message contract since the operation has multiple return values.
        [System.ServiceModel.OperationContractAttribute(Action="http://tempuri.org/IService1/ResetPassword", ReplyAction="http://tempuri.org/IService1/ResetPasswordResponse")]
        System.Threading.Tasks.Task<ProtegeGXService.ResetPasswordResponse> ResetPasswordAsync(ProtegeGXService.ResetPasswordRequest request);
        
        // CODEGEN: Generating message contract since the operation has multiple return values.
        [System.ServiceModel.OperationContractAttribute(Action="http://tempuri.org/IService1/GetStatusListStatus", ReplyAction="http://tempuri.org/IService1/GetStatusListStatusResponse")]
        System.Threading.Tasks.Task<ProtegeGXService.GetStatusListStatusResponse> GetStatusListStatusAsync(ProtegeGXService.GetStatusListStatusRequest request);
        
        // CODEGEN: Generating message contract since the operation has multiple return values.
        [System.ServiceModel.OperationContractAttribute(Action="http://tempuri.org/IService1/GetLatestEvents", ReplyAction="http://tempuri.org/IService1/GetLatestEventsResponse")]
        System.Threading.Tasks.Task<ProtegeGXService.GetLatestEventsResponse> GetLatestEventsAsync(ProtegeGXService.GetLatestEventsRequest request);
        
        // CODEGEN: Generating message contract since the operation has multiple return values.
        [System.ServiceModel.OperationContractAttribute(Action="http://tempuri.org/IService1/RunReportDefaults", ReplyAction="http://tempuri.org/IService1/RunReportDefaultsResponse")]
        System.Threading.Tasks.Task<ProtegeGXService.RunReportDefaultsResponse> RunReportDefaultsAsync(ProtegeGXService.RunReportDefaultsRequest request);
        
        // CODEGEN: Generating message contract since the operation has multiple return values.
        [System.ServiceModel.OperationContractAttribute(Action="http://tempuri.org/IService1/RunReport", ReplyAction="http://tempuri.org/IService1/RunReportResponse")]
        System.Threading.Tasks.Task<ProtegeGXService.RunReportResponse> RunReportAsync(ProtegeGXService.RunReportRequest request);
        
        // CODEGEN: Generating message contract since the operation has multiple return values.
        [System.ServiceModel.OperationContractAttribute(Action="http://tempuri.org/IService1/FindRecords", ReplyAction="http://tempuri.org/IService1/FindRecordsResponse")]
        System.Threading.Tasks.Task<ProtegeGXService.FindRecordsResponse> FindRecordsAsync(ProtegeGXService.FindRecordsRequest request);
        
        // CODEGEN: Generating message contract since the operation has multiple return values.
        [System.ServiceModel.OperationContractAttribute(Action="http://tempuri.org/IService1/GetImageData", ReplyAction="http://tempuri.org/IService1/GetImageDataResponse")]
        System.Threading.Tasks.Task<ProtegeGXService.GetImageDataResponse> GetImageDataAsync(ProtegeGXService.GetImageDataRequest request);
        
        // CODEGEN: Generating message contract since the operation has multiple return values.
        [System.ServiceModel.OperationContractAttribute(Action="http://tempuri.org/IService1/GetVersion", ReplyAction="http://tempuri.org/IService1/GetVersionResponse")]
        System.Threading.Tasks.Task<ProtegeGXService.GetVersionResponse> GetVersionAsync(ProtegeGXService.GetVersionRequest request);
        
        // CODEGEN: Generating message contract since the operation has multiple return values.
        [System.ServiceModel.OperationContractAttribute(Action="http://tempuri.org/IService1/PrepareEventReport", ReplyAction="http://tempuri.org/IService1/PrepareEventReportResponse")]
        System.Threading.Tasks.Task<ProtegeGXService.PrepareEventReportResponse> PrepareEventReportAsync(ProtegeGXService.PrepareEventReportRequest request);
        
        // CODEGEN: Generating message contract since the operation has multiple return values.
        [System.ServiceModel.OperationContractAttribute(Action="http://tempuri.org/IService1/ExecuteEventReport", ReplyAction="http://tempuri.org/IService1/ExecuteEventReportResponse")]
        System.Threading.Tasks.Task<ProtegeGXService.ExecuteEventReportResponse> ExecuteEventReportAsync(ProtegeGXService.ExecuteEventReportRequest request);
        
        // CODEGEN: Generating message contract since the operation has multiple return values.
        [System.ServiceModel.OperationContractAttribute(Action="http://tempuri.org/IService1/GetEventReportData", ReplyAction="http://tempuri.org/IService1/GetEventReportDataResponse")]
        System.Threading.Tasks.Task<ProtegeGXService.GetEventReportDataResponse> GetEventReportDataAsync(ProtegeGXService.GetEventReportDataRequest request);
        
        // CODEGEN: Generating message contract since the operation has multiple return values.
        [System.ServiceModel.OperationContractAttribute(Action="http://tempuri.org/IService1/GetMostRecentEventID", ReplyAction="http://tempuri.org/IService1/GetMostRecentEventIDResponse")]
        System.Threading.Tasks.Task<ProtegeGXService.GetMostRecentEventIDResponse> GetMostRecentEventIDAsync(ProtegeGXService.GetMostRecentEventIDRequest request);
        
        // CODEGEN: Generating message contract since the operation has multiple return values.
        [System.ServiceModel.OperationContractAttribute(Action="http://tempuri.org/IService1/GetTableSchema", ReplyAction="http://tempuri.org/IService1/GetTableSchemaResponse")]
        System.Threading.Tasks.Task<ProtegeGXService.GetTableSchemaResponse> GetTableSchemaAsync(ProtegeGXService.GetTableSchemaRequest request);
        
        // CODEGEN: Generating message contract since the operation has multiple return values.
        [System.ServiceModel.OperationContractAttribute(Action="http://tempuri.org/IService1/GetTableIDNameMapping", ReplyAction="http://tempuri.org/IService1/GetTableIDNameMappingResponse")]
        System.Threading.Tasks.Task<ProtegeGXService.GetTableIDNameMappingResponse> GetTableIDNameMappingAsync(ProtegeGXService.GetTableIDNameMappingRequest request);
        
        // CODEGEN: Generating message contract since the operation has multiple return values.
        [System.ServiceModel.OperationContractAttribute(Action="http://tempuri.org/IService1/WhoAmI", ReplyAction="http://tempuri.org/IService1/WhoAmIResponse")]
        System.Threading.Tasks.Task<ProtegeGXService.WhoAmIResponse> WhoAmIAsync(ProtegeGXService.WhoAmIRequest request);
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.2.0-preview1.23462.5")]
    [System.ServiceModel.MessageContractAttribute(WrapperName="AddRecord", WrapperNamespace="http://tempuri.org/", IsWrapped=true)]
    public partial class AddRecordRequest
    {
        
        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="http://tempuri.org/", Order=0)]
        public ProtegeGXService.Logon LD;
        
        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="http://tempuri.org/", Order=1)]
        public long nTableID;
        
        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="http://tempuri.org/", Order=2)]
        public long nSiteID;
        
        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="http://tempuri.org/", Order=3)]
        public string strXML;
        
        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="http://tempuri.org/", Order=4)]
        public long nNewID;
        
        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="http://tempuri.org/", Order=5)]
        public long nErrorCode;
        
        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="http://tempuri.org/", Order=6)]
        public string strErrorXML;
        
        public AddRecordRequest()
        {
        }
        
        public AddRecordRequest(ProtegeGXService.Logon LD, long nTableID, long nSiteID, string strXML, long nNewID, long nErrorCode, string strErrorXML)
        {
            this.LD = LD;
            this.nTableID = nTableID;
            this.nSiteID = nSiteID;
            this.strXML = strXML;
            this.nNewID = nNewID;
            this.nErrorCode = nErrorCode;
            this.strErrorXML = strErrorXML;
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.2.0-preview1.23462.5")]
    [System.ServiceModel.MessageContractAttribute(WrapperName="AddRecordResponse", WrapperNamespace="http://tempuri.org/", IsWrapped=true)]
    public partial class AddRecordResponse
    {
        
        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="http://tempuri.org/", Order=0)]
        public bool AddRecordResult;
        
        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="http://tempuri.org/", Order=1)]
        public long nNewID;
        
        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="http://tempuri.org/", Order=2)]
        public long nErrorCode;
        
        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="http://tempuri.org/", Order=3)]
        public string strErrorXML;
        
        public AddRecordResponse()
        {
        }
        
        public AddRecordResponse(bool AddRecordResult, long nNewID, long nErrorCode, string strErrorXML)
        {
            this.AddRecordResult = AddRecordResult;
            this.nNewID = nNewID;
            this.nErrorCode = nErrorCode;
            this.strErrorXML = strErrorXML;
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.2.0-preview1.23462.5")]
    [System.ServiceModel.MessageContractAttribute(WrapperName="AddRecordSC", WrapperNamespace="http://tempuri.org/", IsWrapped=true)]
    public partial class AddRecordSCRequest
    {
        
        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="http://tempuri.org/", Order=0)]
        public ProtegeGXService.Logon LD;
        
        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="http://tempuri.org/", Order=1)]
        public long nTableID;
        
        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="http://tempuri.org/", Order=2)]
        public long nSiteID;
        
        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="http://tempuri.org/", Order=3)]
        public long nControllerID;
        
        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="http://tempuri.org/", Order=4)]
        public string strXML;
        
        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="http://tempuri.org/", Order=5)]
        public long nNewID;
        
        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="http://tempuri.org/", Order=6)]
        public long nErrorCode;
        
        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="http://tempuri.org/", Order=7)]
        public string strErrorXML;
        
        public AddRecordSCRequest()
        {
        }
        
        public AddRecordSCRequest(ProtegeGXService.Logon LD, long nTableID, long nSiteID, long nControllerID, string strXML, long nNewID, long nErrorCode, string strErrorXML)
        {
            this.LD = LD;
            this.nTableID = nTableID;
            this.nSiteID = nSiteID;
            this.nControllerID = nControllerID;
            this.strXML = strXML;
            this.nNewID = nNewID;
            this.nErrorCode = nErrorCode;
            this.strErrorXML = strErrorXML;
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.2.0-preview1.23462.5")]
    [System.ServiceModel.MessageContractAttribute(WrapperName="AddRecordSCResponse", WrapperNamespace="http://tempuri.org/", IsWrapped=true)]
    public partial class AddRecordSCResponse
    {
        
        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="http://tempuri.org/", Order=0)]
        public bool AddRecordSCResult;
        
        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="http://tempuri.org/", Order=1)]
        public long nNewID;
        
        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="http://tempuri.org/", Order=2)]
        public long nErrorCode;
        
        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="http://tempuri.org/", Order=3)]
        public string strErrorXML;
        
        public AddRecordSCResponse()
        {
        }
        
        public AddRecordSCResponse(bool AddRecordSCResult, long nNewID, long nErrorCode, string strErrorXML)
        {
            this.AddRecordSCResult = AddRecordSCResult;
            this.nNewID = nNewID;
            this.nErrorCode = nErrorCode;
            this.strErrorXML = strErrorXML;
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.2.0-preview1.23462.5")]
    [System.ServiceModel.MessageContractAttribute(WrapperName="UpdateRecord", WrapperNamespace="http://tempuri.org/", IsWrapped=true)]
    public partial class UpdateRecordRequest
    {
        
        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="http://tempuri.org/", Order=0)]
        public ProtegeGXService.Logon LD;
        
        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="http://tempuri.org/", Order=1)]
        public long nTableID;
        
        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="http://tempuri.org/", Order=2)]
        public long nParentID;
        
        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="http://tempuri.org/", Order=3)]
        public long nRecordID;
        
        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="http://tempuri.org/", Order=4)]
        public string strXML;
        
        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="http://tempuri.org/", Order=5)]
        public long nErrorCode;
        
        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="http://tempuri.org/", Order=6)]
        public string strErrorXML;
        
        public UpdateRecordRequest()
        {
        }
        
        public UpdateRecordRequest(ProtegeGXService.Logon LD, long nTableID, long nParentID, long nRecordID, string strXML, long nErrorCode, string strErrorXML)
        {
            this.LD = LD;
            this.nTableID = nTableID;
            this.nParentID = nParentID;
            this.nRecordID = nRecordID;
            this.strXML = strXML;
            this.nErrorCode = nErrorCode;
            this.strErrorXML = strErrorXML;
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.2.0-preview1.23462.5")]
    [System.ServiceModel.MessageContractAttribute(WrapperName="UpdateRecordResponse", WrapperNamespace="http://tempuri.org/", IsWrapped=true)]
    public partial class UpdateRecordResponse
    {
        
        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="http://tempuri.org/", Order=0)]
        public bool UpdateRecordResult;
        
        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="http://tempuri.org/", Order=1)]
        public long nErrorCode;
        
        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="http://tempuri.org/", Order=2)]
        public string strErrorXML;
        
        public UpdateRecordResponse()
        {
        }
        
        public UpdateRecordResponse(bool UpdateRecordResult, long nErrorCode, string strErrorXML)
        {
            this.UpdateRecordResult = UpdateRecordResult;
            this.nErrorCode = nErrorCode;
            this.strErrorXML = strErrorXML;
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.2.0-preview1.23462.5")]
    [System.ServiceModel.MessageContractAttribute(WrapperName="GetRecord", WrapperNamespace="http://tempuri.org/", IsWrapped=true)]
    public partial class GetRecordRequest
    {
        
        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="http://tempuri.org/", Order=0)]
        public ProtegeGXService.Logon LD;
        
        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="http://tempuri.org/", Order=1)]
        public long nTableID;
        
        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="http://tempuri.org/", Order=2)]
        public long nParentID;
        
        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="http://tempuri.org/", Order=3)]
        public long nRecordID;
        
        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="http://tempuri.org/", Order=4)]
        public string strXML;
        
        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="http://tempuri.org/", Order=5)]
        public long nErrorCode;
        
        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="http://tempuri.org/", Order=6)]
        public string strErrorXML;
        
        public GetRecordRequest()
        {
        }
        
        public GetRecordRequest(ProtegeGXService.Logon LD, long nTableID, long nParentID, long nRecordID, string strXML, long nErrorCode, string strErrorXML)
        {
            this.LD = LD;
            this.nTableID = nTableID;
            this.nParentID = nParentID;
            this.nRecordID = nRecordID;
            this.strXML = strXML;
            this.nErrorCode = nErrorCode;
            this.strErrorXML = strErrorXML;
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.2.0-preview1.23462.5")]
    [System.ServiceModel.MessageContractAttribute(WrapperName="GetRecordResponse", WrapperNamespace="http://tempuri.org/", IsWrapped=true)]
    public partial class GetRecordResponse
    {
        
        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="http://tempuri.org/", Order=0)]
        public bool GetRecordResult;
        
        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="http://tempuri.org/", Order=1)]
        public string strXML;
        
        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="http://tempuri.org/", Order=2)]
        public long nErrorCode;
        
        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="http://tempuri.org/", Order=3)]
        public string strErrorXML;
        
        public GetRecordResponse()
        {
        }
        
        public GetRecordResponse(bool GetRecordResult, string strXML, long nErrorCode, string strErrorXML)
        {
            this.GetRecordResult = GetRecordResult;
            this.strXML = strXML;
            this.nErrorCode = nErrorCode;
            this.strErrorXML = strErrorXML;
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.2.0-preview1.23462.5")]
    [System.ServiceModel.MessageContractAttribute(WrapperName="DeleteRecord", WrapperNamespace="http://tempuri.org/", IsWrapped=true)]
    public partial class DeleteRecordRequest
    {
        
        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="http://tempuri.org/", Order=0)]
        public ProtegeGXService.Logon LD;
        
        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="http://tempuri.org/", Order=1)]
        public long nTableID;
        
        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="http://tempuri.org/", Order=2)]
        public long nParentID;
        
        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="http://tempuri.org/", Order=3)]
        public long nRecordID;
        
        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="http://tempuri.org/", Order=4)]
        public long nErrorCode;
        
        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="http://tempuri.org/", Order=5)]
        public string strErrorXML;
        
        public DeleteRecordRequest()
        {
        }
        
        public DeleteRecordRequest(ProtegeGXService.Logon LD, long nTableID, long nParentID, long nRecordID, long nErrorCode, string strErrorXML)
        {
            this.LD = LD;
            this.nTableID = nTableID;
            this.nParentID = nParentID;
            this.nRecordID = nRecordID;
            this.nErrorCode = nErrorCode;
            this.strErrorXML = strErrorXML;
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.2.0-preview1.23462.5")]
    [System.ServiceModel.MessageContractAttribute(WrapperName="DeleteRecordResponse", WrapperNamespace="http://tempuri.org/", IsWrapped=true)]
    public partial class DeleteRecordResponse
    {
        
        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="http://tempuri.org/", Order=0)]
        public bool DeleteRecordResult;
        
        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="http://tempuri.org/", Order=1)]
        public long nErrorCode;
        
        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="http://tempuri.org/", Order=2)]
        public string strErrorXML;
        
        public DeleteRecordResponse()
        {
        }
        
        public DeleteRecordResponse(bool DeleteRecordResult, long nErrorCode, string strErrorXML)
        {
            this.DeleteRecordResult = DeleteRecordResult;
            this.nErrorCode = nErrorCode;
            this.strErrorXML = strErrorXML;
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.2.0-preview1.23462.5")]
    [System.ServiceModel.MessageContractAttribute(WrapperName="ListRecords", WrapperNamespace="http://tempuri.org/", IsWrapped=true)]
    public partial class ListRecordsRequest
    {
        
        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="http://tempuri.org/", Order=0)]
        public ProtegeGXService.Logon LD;
        
        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="http://tempuri.org/", Order=1)]
        public long nTableID;
        
        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="http://tempuri.org/", Order=2)]
        public long nParentID;
        
        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="http://tempuri.org/", Order=3)]
        public long nStart;
        
        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="http://tempuri.org/", Order=4)]
        public long nNumberOfRows;
        
        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="http://tempuri.org/", Order=5)]
        public long nRecordGroup;
        
        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="http://tempuri.org/", Order=6)]
        public long nReserved;
        
        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="http://tempuri.org/", Order=7)]
        public bool bAtStart;
        
        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="http://tempuri.org/", Order=8)]
        public bool bAtEnd;
        
        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="http://tempuri.org/", Order=9)]
        public ProtegeGXService.DeviceNames RData;
        
        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="http://tempuri.org/", Order=10)]
        public long nErrorCode;
        
        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="http://tempuri.org/", Order=11)]
        public string strErrorXML;
        
        public ListRecordsRequest()
        {
        }
        
        public ListRecordsRequest(ProtegeGXService.Logon LD, long nTableID, long nParentID, long nStart, long nNumberOfRows, long nRecordGroup, long nReserved, bool bAtStart, bool bAtEnd, ProtegeGXService.DeviceNames RData, long nErrorCode, string strErrorXML)
        {
            this.LD = LD;
            this.nTableID = nTableID;
            this.nParentID = nParentID;
            this.nStart = nStart;
            this.nNumberOfRows = nNumberOfRows;
            this.nRecordGroup = nRecordGroup;
            this.nReserved = nReserved;
            this.bAtStart = bAtStart;
            this.bAtEnd = bAtEnd;
            this.RData = RData;
            this.nErrorCode = nErrorCode;
            this.strErrorXML = strErrorXML;
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.2.0-preview1.23462.5")]
    [System.ServiceModel.MessageContractAttribute(WrapperName="ListRecordsResponse", WrapperNamespace="http://tempuri.org/", IsWrapped=true)]
    public partial class ListRecordsResponse
    {
        
        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="http://tempuri.org/", Order=0)]
        public bool ListRecordsResult;
        
        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="http://tempuri.org/", Order=1)]
        public bool bAtStart;
        
        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="http://tempuri.org/", Order=2)]
        public bool bAtEnd;
        
        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="http://tempuri.org/", Order=3)]
        public ProtegeGXService.DeviceNames RData;
        
        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="http://tempuri.org/", Order=4)]
        public long nErrorCode;
        
        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="http://tempuri.org/", Order=5)]
        public string strErrorXML;
        
        public ListRecordsResponse()
        {
        }
        
        public ListRecordsResponse(bool ListRecordsResult, bool bAtStart, bool bAtEnd, ProtegeGXService.DeviceNames RData, long nErrorCode, string strErrorXML)
        {
            this.ListRecordsResult = ListRecordsResult;
            this.bAtStart = bAtStart;
            this.bAtEnd = bAtEnd;
            this.RData = RData;
            this.nErrorCode = nErrorCode;
            this.strErrorXML = strErrorXML;
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.2.0-preview1.23462.5")]
    [System.ServiceModel.MessageContractAttribute(WrapperName="EventReportByRecordGroup", WrapperNamespace="http://tempuri.org/", IsWrapped=true)]
    public partial class EventReportByRecordGroupRequest
    {
        
        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="http://tempuri.org/", Order=0)]
        public ProtegeGXService.Logon LD;
        
        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="http://tempuri.org/", Order=1)]
        public int nSiteID;
        
        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="http://tempuri.org/", Order=2)]
        public int nRecordGroup;
        
        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="http://tempuri.org/", Order=3)]
        public long nStart;
        
        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="http://tempuri.org/", Order=4)]
        public long nNumberOfRows;
        
        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="http://tempuri.org/", Order=5)]
        public int nLanguage;
        
        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="http://tempuri.org/", Order=6)]
        public string strStartDate;
        
        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="http://tempuri.org/", Order=7)]
        public bool bUseStartDate;
        
        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="http://tempuri.org/", Order=8)]
        public string strEndDate;
        
        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="http://tempuri.org/", Order=9)]
        public bool bUseEndDate;
        
        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="http://tempuri.org/", Order=10)]
        public ProtegeGXService.ReportDataCollection Names;
        
        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="http://tempuri.org/", Order=11)]
        public int nErrorCode;
        
        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="http://tempuri.org/", Order=12)]
        public string strErrorXML;
        
        public EventReportByRecordGroupRequest()
        {
        }
        
        public EventReportByRecordGroupRequest(ProtegeGXService.Logon LD, int nSiteID, int nRecordGroup, long nStart, long nNumberOfRows, int nLanguage, string strStartDate, bool bUseStartDate, string strEndDate, bool bUseEndDate, ProtegeGXService.ReportDataCollection Names, int nErrorCode, string strErrorXML)
        {
            this.LD = LD;
            this.nSiteID = nSiteID;
            this.nRecordGroup = nRecordGroup;
            this.nStart = nStart;
            this.nNumberOfRows = nNumberOfRows;
            this.nLanguage = nLanguage;
            this.strStartDate = strStartDate;
            this.bUseStartDate = bUseStartDate;
            this.strEndDate = strEndDate;
            this.bUseEndDate = bUseEndDate;
            this.Names = Names;
            this.nErrorCode = nErrorCode;
            this.strErrorXML = strErrorXML;
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.2.0-preview1.23462.5")]
    [System.ServiceModel.MessageContractAttribute(WrapperName="EventReportByRecordGroupResponse", WrapperNamespace="http://tempuri.org/", IsWrapped=true)]
    public partial class EventReportByRecordGroupResponse
    {
        
        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="http://tempuri.org/", Order=0)]
        public bool EventReportByRecordGroupResult;
        
        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="http://tempuri.org/", Order=1)]
        public ProtegeGXService.ReportDataCollection Names;
        
        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="http://tempuri.org/", Order=2)]
        public int nErrorCode;
        
        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="http://tempuri.org/", Order=3)]
        public string strErrorXML;
        
        public EventReportByRecordGroupResponse()
        {
        }
        
        public EventReportByRecordGroupResponse(bool EventReportByRecordGroupResult, ProtegeGXService.ReportDataCollection Names, int nErrorCode, string strErrorXML)
        {
            this.EventReportByRecordGroupResult = EventReportByRecordGroupResult;
            this.Names = Names;
            this.nErrorCode = nErrorCode;
            this.strErrorXML = strErrorXML;
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.2.0-preview1.23462.5")]
    [System.ServiceModel.MessageContractAttribute(WrapperName="EventReportByUser", WrapperNamespace="http://tempuri.org/", IsWrapped=true)]
    public partial class EventReportByUserRequest
    {
        
        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="http://tempuri.org/", Order=0)]
        public ProtegeGXService.Logon LD;
        
        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="http://tempuri.org/", Order=1)]
        public int nSiteID;
        
        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="http://tempuri.org/", Order=2)]
        public int nUserID;
        
        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="http://tempuri.org/", Order=3)]
        public long nStart;
        
        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="http://tempuri.org/", Order=4)]
        public long nNumberOfRows;
        
        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="http://tempuri.org/", Order=5)]
        public int nLanguage;
        
        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="http://tempuri.org/", Order=6)]
        public string strStartDate;
        
        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="http://tempuri.org/", Order=7)]
        public bool bUseStartDate;
        
        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="http://tempuri.org/", Order=8)]
        public string strEndDate;
        
        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="http://tempuri.org/", Order=9)]
        public bool bUseEndDate;
        
        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="http://tempuri.org/", Order=10)]
        public ProtegeGXService.ReportDataCollection Data;
        
        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="http://tempuri.org/", Order=11)]
        public int nErrorCode;
        
        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="http://tempuri.org/", Order=12)]
        public string strErrorXML;
        
        public EventReportByUserRequest()
        {
        }
        
        public EventReportByUserRequest(ProtegeGXService.Logon LD, int nSiteID, int nUserID, long nStart, long nNumberOfRows, int nLanguage, string strStartDate, bool bUseStartDate, string strEndDate, bool bUseEndDate, ProtegeGXService.ReportDataCollection Data, int nErrorCode, string strErrorXML)
        {
            this.LD = LD;
            this.nSiteID = nSiteID;
            this.nUserID = nUserID;
            this.nStart = nStart;
            this.nNumberOfRows = nNumberOfRows;
            this.nLanguage = nLanguage;
            this.strStartDate = strStartDate;
            this.bUseStartDate = bUseStartDate;
            this.strEndDate = strEndDate;
            this.bUseEndDate = bUseEndDate;
            this.Data = Data;
            this.nErrorCode = nErrorCode;
            this.strErrorXML = strErrorXML;
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.2.0-preview1.23462.5")]
    [System.ServiceModel.MessageContractAttribute(WrapperName="EventReportByUserResponse", WrapperNamespace="http://tempuri.org/", IsWrapped=true)]
    public partial class EventReportByUserResponse
    {
        
        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="http://tempuri.org/", Order=0)]
        public bool EventReportByUserResult;
        
        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="http://tempuri.org/", Order=1)]
        public ProtegeGXService.ReportDataCollection Data;
        
        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="http://tempuri.org/", Order=2)]
        public int nErrorCode;
        
        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="http://tempuri.org/", Order=3)]
        public string strErrorXML;
        
        public EventReportByUserResponse()
        {
        }
        
        public EventReportByUserResponse(bool EventReportByUserResult, ProtegeGXService.ReportDataCollection Data, int nErrorCode, string strErrorXML)
        {
            this.EventReportByUserResult = EventReportByUserResult;
            this.Data = Data;
            this.nErrorCode = nErrorCode;
            this.strErrorXML = strErrorXML;
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.2.0-preview1.23462.5")]
    [System.ServiceModel.MessageContractAttribute(WrapperName="SetLogon", WrapperNamespace="http://tempuri.org/", IsWrapped=true)]
    public partial class SetLogonRequest
    {
        
        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="http://tempuri.org/", Order=0)]
        public ProtegeGXService.LogonData LD;
        
        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="http://tempuri.org/", Order=1)]
        public ProtegeGXService.Logon L;
        
        public SetLogonRequest()
        {
        }
        
        public SetLogonRequest(ProtegeGXService.LogonData LD, ProtegeGXService.Logon L)
        {
            this.LD = LD;
            this.L = L;
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.2.0-preview1.23462.5")]
    [System.ServiceModel.MessageContractAttribute(WrapperName="SetLogonResponse", WrapperNamespace="http://tempuri.org/", IsWrapped=true)]
    public partial class SetLogonResponse
    {
        
        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="http://tempuri.org/", Order=0)]
        public ProtegeGXService.LogonData LD;
        
        public SetLogonResponse()
        {
        }
        
        public SetLogonResponse(ProtegeGXService.LogonData LD)
        {
            this.LD = LD;
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.2.0-preview1.23462.5")]
    [System.ServiceModel.MessageContractAttribute(WrapperName="TestMainDatabaseConnection", WrapperNamespace="http://tempuri.org/", IsWrapped=true)]
    public partial class TestMainDatabaseConnectionRequest
    {
        
        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="http://tempuri.org/", Order=0)]
        public string strError;
        
        public TestMainDatabaseConnectionRequest()
        {
        }
        
        public TestMainDatabaseConnectionRequest(string strError)
        {
            this.strError = strError;
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.2.0-preview1.23462.5")]
    [System.ServiceModel.MessageContractAttribute(WrapperName="TestMainDatabaseConnectionResponse", WrapperNamespace="http://tempuri.org/", IsWrapped=true)]
    public partial class TestMainDatabaseConnectionResponse
    {
        
        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="http://tempuri.org/", Order=0)]
        public bool TestMainDatabaseConnectionResult;
        
        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="http://tempuri.org/", Order=1)]
        public string strError;
        
        public TestMainDatabaseConnectionResponse()
        {
        }
        
        public TestMainDatabaseConnectionResponse(bool TestMainDatabaseConnectionResult, string strError)
        {
            this.TestMainDatabaseConnectionResult = TestMainDatabaseConnectionResult;
            this.strError = strError;
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.2.0-preview1.23462.5")]
    [System.ServiceModel.MessageContractAttribute(WrapperName="TestEventDatabaseConnection", WrapperNamespace="http://tempuri.org/", IsWrapped=true)]
    public partial class TestEventDatabaseConnectionRequest
    {
        
        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="http://tempuri.org/", Order=0)]
        public string strError;
        
        public TestEventDatabaseConnectionRequest()
        {
        }
        
        public TestEventDatabaseConnectionRequest(string strError)
        {
            this.strError = strError;
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.2.0-preview1.23462.5")]
    [System.ServiceModel.MessageContractAttribute(WrapperName="TestEventDatabaseConnectionResponse", WrapperNamespace="http://tempuri.org/", IsWrapped=true)]
    public partial class TestEventDatabaseConnectionResponse
    {
        
        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="http://tempuri.org/", Order=0)]
        public bool TestEventDatabaseConnectionResult;
        
        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="http://tempuri.org/", Order=1)]
        public string strError;
        
        public TestEventDatabaseConnectionResponse()
        {
        }
        
        public TestEventDatabaseConnectionResponse(bool TestEventDatabaseConnectionResult, string strError)
        {
            this.TestEventDatabaseConnectionResult = TestEventDatabaseConnectionResult;
            this.strError = strError;
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.2.0-preview1.23462.5")]
    [System.ServiceModel.MessageContractAttribute(WrapperName="GetDeviceStatus", WrapperNamespace="http://tempuri.org/", IsWrapped=true)]
    public partial class GetDeviceStatusRequest
    {
        
        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="http://tempuri.org/", Order=0)]
        public ProtegeGXService.Logon LD;
        
        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="http://tempuri.org/", Order=1)]
        public long nControllerID;
        
        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="http://tempuri.org/", Order=2)]
        public long nType;
        
        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="http://tempuri.org/", Order=3)]
        public long nAddress;
        
        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="http://tempuri.org/", Order=4)]
        public long nAddress2;
        
        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="http://tempuri.org/", Order=5)]
        public long nStatus1;
        
        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="http://tempuri.org/", Order=6)]
        public long nStatus2;
        
        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="http://tempuri.org/", Order=7)]
        public long nStatus3;
        
        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="http://tempuri.org/", Order=8)]
        public long nErrorCode;
        
        public GetDeviceStatusRequest()
        {
        }
        
        public GetDeviceStatusRequest(ProtegeGXService.Logon LD, long nControllerID, long nType, long nAddress, long nAddress2, long nStatus1, long nStatus2, long nStatus3, long nErrorCode)
        {
            this.LD = LD;
            this.nControllerID = nControllerID;
            this.nType = nType;
            this.nAddress = nAddress;
            this.nAddress2 = nAddress2;
            this.nStatus1 = nStatus1;
            this.nStatus2 = nStatus2;
            this.nStatus3 = nStatus3;
            this.nErrorCode = nErrorCode;
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.2.0-preview1.23462.5")]
    [System.ServiceModel.MessageContractAttribute(WrapperName="GetDeviceStatusResponse", WrapperNamespace="http://tempuri.org/", IsWrapped=true)]
    public partial class GetDeviceStatusResponse
    {
        
        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="http://tempuri.org/", Order=0)]
        public bool GetDeviceStatusResult;
        
        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="http://tempuri.org/", Order=1)]
        public long nStatus1;
        
        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="http://tempuri.org/", Order=2)]
        public long nStatus2;
        
        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="http://tempuri.org/", Order=3)]
        public long nStatus3;
        
        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="http://tempuri.org/", Order=4)]
        public long nErrorCode;
        
        public GetDeviceStatusResponse()
        {
        }
        
        public GetDeviceStatusResponse(bool GetDeviceStatusResult, long nStatus1, long nStatus2, long nStatus3, long nErrorCode)
        {
            this.GetDeviceStatusResult = GetDeviceStatusResult;
            this.nStatus1 = nStatus1;
            this.nStatus2 = nStatus2;
            this.nStatus3 = nStatus3;
            this.nErrorCode = nErrorCode;
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.2.0-preview1.23462.5")]
    [System.ServiceModel.MessageContractAttribute(WrapperName="SetDateTime", WrapperNamespace="http://tempuri.org/", IsWrapped=true)]
    public partial class SetDateTimeRequest
    {
        
        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="http://tempuri.org/", Order=0)]
        public ProtegeGXService.Logon LD;
        
        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="http://tempuri.org/", Order=1)]
        public long nSiteID;
        
        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="http://tempuri.org/", Order=2)]
        public long nControllerID;
        
        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="http://tempuri.org/", Order=3)]
        public System.DateTime dtTime;
        
        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="http://tempuri.org/", Order=4)]
        public System.DateTime dtUTCTime;
        
        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="http://tempuri.org/", Order=5)]
        public long pCookie;
        
        public SetDateTimeRequest()
        {
        }
        
        public SetDateTimeRequest(ProtegeGXService.Logon LD, long nSiteID, long nControllerID, System.DateTime dtTime, System.DateTime dtUTCTime, long pCookie)
        {
            this.LD = LD;
            this.nSiteID = nSiteID;
            this.nControllerID = nControllerID;
            this.dtTime = dtTime;
            this.dtUTCTime = dtUTCTime;
            this.pCookie = pCookie;
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.2.0-preview1.23462.5")]
    [System.ServiceModel.MessageContractAttribute(WrapperName="SetDateTimeResponse", WrapperNamespace="http://tempuri.org/", IsWrapped=true)]
    public partial class SetDateTimeResponse
    {
        
        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="http://tempuri.org/", Order=0)]
        public bool SetDateTimeResult;
        
        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="http://tempuri.org/", Order=1)]
        public long pCookie;
        
        public SetDateTimeResponse()
        {
        }
        
        public SetDateTimeResponse(bool SetDateTimeResult, long pCookie)
        {
            this.SetDateTimeResult = SetDateTimeResult;
            this.pCookie = pCookie;
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.2.0-preview1.23462.5")]
    [System.ServiceModel.MessageContractAttribute(WrapperName="InitialiseKabaKey", WrapperNamespace="http://tempuri.org/", IsWrapped=true)]
    public partial class InitialiseKabaKeyRequest
    {
        
        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="http://tempuri.org/", Order=0)]
        public ProtegeGXService.Logon LD;
        
        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="http://tempuri.org/", Order=1)]
        public long nSiteID;
        
        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="http://tempuri.org/", Order=2)]
        public long nUserID;
        
        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="http://tempuri.org/", Order=3)]
        public string sKabaKeyBoxID;
        
        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="http://tempuri.org/", Order=4)]
        public string sFromCaller;
        
        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="http://tempuri.org/", Order=5)]
        public string sKabaKeyID;
        
        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="http://tempuri.org/", Order=6)]
        public string sErrorCode;
        
        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="http://tempuri.org/", Order=7)]
        public string sDescription;
        
        public InitialiseKabaKeyRequest()
        {
        }
        
        public InitialiseKabaKeyRequest(ProtegeGXService.Logon LD, long nSiteID, long nUserID, string sKabaKeyBoxID, string sFromCaller, string sKabaKeyID, string sErrorCode, string sDescription)
        {
            this.LD = LD;
            this.nSiteID = nSiteID;
            this.nUserID = nUserID;
            this.sKabaKeyBoxID = sKabaKeyBoxID;
            this.sFromCaller = sFromCaller;
            this.sKabaKeyID = sKabaKeyID;
            this.sErrorCode = sErrorCode;
            this.sDescription = sDescription;
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.2.0-preview1.23462.5")]
    [System.ServiceModel.MessageContractAttribute(WrapperName="InitialiseKabaKeyResponse", WrapperNamespace="http://tempuri.org/", IsWrapped=true)]
    public partial class InitialiseKabaKeyResponse
    {
        
        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="http://tempuri.org/", Order=0)]
        public bool InitialiseKabaKeyResult;
        
        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="http://tempuri.org/", Order=1)]
        public string sKabaKeyID;
        
        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="http://tempuri.org/", Order=2)]
        public string sErrorCode;
        
        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="http://tempuri.org/", Order=3)]
        public string sDescription;
        
        public InitialiseKabaKeyResponse()
        {
        }
        
        public InitialiseKabaKeyResponse(bool InitialiseKabaKeyResult, string sKabaKeyID, string sErrorCode, string sDescription)
        {
            this.InitialiseKabaKeyResult = InitialiseKabaKeyResult;
            this.sKabaKeyID = sKabaKeyID;
            this.sErrorCode = sErrorCode;
            this.sDescription = sDescription;
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.2.0-preview1.23462.5")]
    [System.ServiceModel.MessageContractAttribute(WrapperName="ControlDevice", WrapperNamespace="http://tempuri.org/", IsWrapped=true)]
    public partial class ControlDeviceRequest
    {
        
        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="http://tempuri.org/", Order=0)]
        public ProtegeGXService.Logon LD;
        
        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="http://tempuri.org/", Order=1)]
        public long nSiteID;
        
        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="http://tempuri.org/", Order=2)]
        public long nControllerID;
        
        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="http://tempuri.org/", Order=3)]
        public long nControlType;
        
        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="http://tempuri.org/", Order=4)]
        public long nControlIndex;
        
        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="http://tempuri.org/", Order=5)]
        public long nControlOperation;
        
        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="http://tempuri.org/", Order=6)]
        public long nErrorCode;
        
        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="http://tempuri.org/", Order=7)]
        public long[] list;
        
        public ControlDeviceRequest()
        {
        }
        
        public ControlDeviceRequest(ProtegeGXService.Logon LD, long nSiteID, long nControllerID, long nControlType, long nControlIndex, long nControlOperation, long nErrorCode, long[] list)
        {
            this.LD = LD;
            this.nSiteID = nSiteID;
            this.nControllerID = nControllerID;
            this.nControlType = nControlType;
            this.nControlIndex = nControlIndex;
            this.nControlOperation = nControlOperation;
            this.nErrorCode = nErrorCode;
            this.list = list;
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.2.0-preview1.23462.5")]
    [System.ServiceModel.MessageContractAttribute(WrapperName="ControlDeviceResponse", WrapperNamespace="http://tempuri.org/", IsWrapped=true)]
    public partial class ControlDeviceResponse
    {
        
        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="http://tempuri.org/", Order=0)]
        public bool ControlDeviceResult;
        
        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="http://tempuri.org/", Order=1)]
        public long nErrorCode;
        
        public ControlDeviceResponse()
        {
        }
        
        public ControlDeviceResponse(bool ControlDeviceResult, long nErrorCode)
        {
            this.ControlDeviceResult = ControlDeviceResult;
            this.nErrorCode = nErrorCode;
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.2.0-preview1.23462.5")]
    [System.ServiceModel.MessageContractAttribute(WrapperName="ResetPassword", WrapperNamespace="http://tempuri.org/", IsWrapped=true)]
    public partial class ResetPasswordRequest
    {
        
        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="http://tempuri.org/", Order=0)]
        public ProtegeGXService.Logon LD;
        
        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="http://tempuri.org/", Order=1)]
        public long nOperatorID;
        
        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="http://tempuri.org/", Order=2)]
        public string strNew;
        
        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="http://tempuri.org/", Order=3)]
        public bool bResult;
        
        public ResetPasswordRequest()
        {
        }
        
        public ResetPasswordRequest(ProtegeGXService.Logon LD, long nOperatorID, string strNew, bool bResult)
        {
            this.LD = LD;
            this.nOperatorID = nOperatorID;
            this.strNew = strNew;
            this.bResult = bResult;
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.2.0-preview1.23462.5")]
    [System.ServiceModel.MessageContractAttribute(WrapperName="ResetPasswordResponse", WrapperNamespace="http://tempuri.org/", IsWrapped=true)]
    public partial class ResetPasswordResponse
    {
        
        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="http://tempuri.org/", Order=0)]
        public bool ResetPasswordResult;
        
        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="http://tempuri.org/", Order=1)]
        public bool bResult;
        
        public ResetPasswordResponse()
        {
        }
        
        public ResetPasswordResponse(bool ResetPasswordResult, bool bResult)
        {
            this.ResetPasswordResult = ResetPasswordResult;
            this.bResult = bResult;
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.2.0-preview1.23462.5")]
    [System.ServiceModel.MessageContractAttribute(WrapperName="GetStatusListStatus", WrapperNamespace="http://tempuri.org/", IsWrapped=true)]
    public partial class GetStatusListStatusRequest
    {
        
        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="http://tempuri.org/", Order=0)]
        public ProtegeGXService.Logon LD;
        
        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="http://tempuri.org/", Order=1)]
        public bool bChangesOnly;
        
        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="http://tempuri.org/", Order=2)]
        public System.DateTime dtCompareRefDate;
        
        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="http://tempuri.org/", Order=3)]
        public long nSiteID;
        
        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="http://tempuri.org/", Order=4)]
        public long nStatusListID;
        
        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="http://tempuri.org/", Order=5)]
        public System.DateTime dtRef;
        
        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="http://tempuri.org/", Order=6)]
        public string strXML;
        
        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="http://tempuri.org/", Order=7)]
        public long nErrorCode;
        
        public GetStatusListStatusRequest()
        {
        }
        
        public GetStatusListStatusRequest(ProtegeGXService.Logon LD, bool bChangesOnly, System.DateTime dtCompareRefDate, long nSiteID, long nStatusListID, System.DateTime dtRef, string strXML, long nErrorCode)
        {
            this.LD = LD;
            this.bChangesOnly = bChangesOnly;
            this.dtCompareRefDate = dtCompareRefDate;
            this.nSiteID = nSiteID;
            this.nStatusListID = nStatusListID;
            this.dtRef = dtRef;
            this.strXML = strXML;
            this.nErrorCode = nErrorCode;
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.2.0-preview1.23462.5")]
    [System.ServiceModel.MessageContractAttribute(WrapperName="GetStatusListStatusResponse", WrapperNamespace="http://tempuri.org/", IsWrapped=true)]
    public partial class GetStatusListStatusResponse
    {
        
        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="http://tempuri.org/", Order=0)]
        public bool GetStatusListStatusResult;
        
        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="http://tempuri.org/", Order=1)]
        public System.DateTime dtRef;
        
        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="http://tempuri.org/", Order=2)]
        public string strXML;
        
        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="http://tempuri.org/", Order=3)]
        public long nErrorCode;
        
        public GetStatusListStatusResponse()
        {
        }
        
        public GetStatusListStatusResponse(bool GetStatusListStatusResult, System.DateTime dtRef, string strXML, long nErrorCode)
        {
            this.GetStatusListStatusResult = GetStatusListStatusResult;
            this.dtRef = dtRef;
            this.strXML = strXML;
            this.nErrorCode = nErrorCode;
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.2.0-preview1.23462.5")]
    [System.ServiceModel.MessageContractAttribute(WrapperName="GetLatestEvents", WrapperNamespace="http://tempuri.org/", IsWrapped=true)]
    public partial class GetLatestEventsRequest
    {
        
        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="http://tempuri.org/", Order=0)]
        public ProtegeGXService.Logon LD;
        
        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="http://tempuri.org/", Order=1)]
        public string strWorkStation;
        
        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="http://tempuri.org/", Order=2)]
        public long nSiteID;
        
        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="http://tempuri.org/", Order=3)]
        public long nReportID;
        
        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="http://tempuri.org/", Order=4)]
        public long nMostRecentCount;
        
        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="http://tempuri.org/", Order=5)]
        public long nLastID;
        
        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="http://tempuri.org/", Order=6)]
        public long nLastAckID;
        
        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="http://tempuri.org/", Order=7)]
        public string strEventReportSettings;
        
        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="http://tempuri.org/", Order=8)]
        public string strEventReportDataCollection;
        
        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="http://tempuri.org/", Order=9)]
        public string strAckReportDataCollection;
        
        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="http://tempuri.org/", Order=10)]
        public long nRecordSetID;
        
        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="http://tempuri.org/", Order=11)]
        public long nNewLastID;
        
        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="http://tempuri.org/", Order=12)]
        public long nNewAckLastID;
        
        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="http://tempuri.org/", Order=13)]
        public long nErrorCode;
        
        public GetLatestEventsRequest()
        {
        }
        
        public GetLatestEventsRequest(ProtegeGXService.Logon LD, string strWorkStation, long nSiteID, long nReportID, long nMostRecentCount, long nLastID, long nLastAckID, string strEventReportSettings, string strEventReportDataCollection, string strAckReportDataCollection, long nRecordSetID, long nNewLastID, long nNewAckLastID, long nErrorCode)
        {
            this.LD = LD;
            this.strWorkStation = strWorkStation;
            this.nSiteID = nSiteID;
            this.nReportID = nReportID;
            this.nMostRecentCount = nMostRecentCount;
            this.nLastID = nLastID;
            this.nLastAckID = nLastAckID;
            this.strEventReportSettings = strEventReportSettings;
            this.strEventReportDataCollection = strEventReportDataCollection;
            this.strAckReportDataCollection = strAckReportDataCollection;
            this.nRecordSetID = nRecordSetID;
            this.nNewLastID = nNewLastID;
            this.nNewAckLastID = nNewAckLastID;
            this.nErrorCode = nErrorCode;
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.2.0-preview1.23462.5")]
    [System.ServiceModel.MessageContractAttribute(WrapperName="GetLatestEventsResponse", WrapperNamespace="http://tempuri.org/", IsWrapped=true)]
    public partial class GetLatestEventsResponse
    {
        
        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="http://tempuri.org/", Order=0)]
        public bool GetLatestEventsResult;
        
        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="http://tempuri.org/", Order=1)]
        public string strEventReportSettings;
        
        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="http://tempuri.org/", Order=2)]
        public string strEventReportDataCollection;
        
        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="http://tempuri.org/", Order=3)]
        public string strAckReportDataCollection;
        
        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="http://tempuri.org/", Order=4)]
        public long nRecordSetID;
        
        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="http://tempuri.org/", Order=5)]
        public long nNewLastID;
        
        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="http://tempuri.org/", Order=6)]
        public long nNewAckLastID;
        
        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="http://tempuri.org/", Order=7)]
        public long nErrorCode;
        
        public GetLatestEventsResponse()
        {
        }
        
        public GetLatestEventsResponse(bool GetLatestEventsResult, string strEventReportSettings, string strEventReportDataCollection, string strAckReportDataCollection, long nRecordSetID, long nNewLastID, long nNewAckLastID, long nErrorCode)
        {
            this.GetLatestEventsResult = GetLatestEventsResult;
            this.strEventReportSettings = strEventReportSettings;
            this.strEventReportDataCollection = strEventReportDataCollection;
            this.strAckReportDataCollection = strAckReportDataCollection;
            this.nRecordSetID = nRecordSetID;
            this.nNewLastID = nNewLastID;
            this.nNewAckLastID = nNewAckLastID;
            this.nErrorCode = nErrorCode;
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.2.0-preview1.23462.5")]
    [System.ServiceModel.MessageContractAttribute(WrapperName="RunReportDefaults", WrapperNamespace="http://tempuri.org/", IsWrapped=true)]
    public partial class RunReportDefaultsRequest
    {
        
        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="http://tempuri.org/", Order=0)]
        public ProtegeGXService.Logon L;
        
        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="http://tempuri.org/", Order=1)]
        public int nReportType;
        
        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="http://tempuri.org/", Order=2)]
        public long nTableID;
        
        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="http://tempuri.org/", Order=3)]
        public long nID;
        
        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="http://tempuri.org/", Order=4)]
        public long nSiteID;
        
        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="http://tempuri.org/", Order=5)]
        public long nStart;
        
        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="http://tempuri.org/", Order=6)]
        public long nNumberOfRows;
        
        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="http://tempuri.org/", Order=7)]
        public string strStartDate;
        
        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="http://tempuri.org/", Order=8)]
        public bool bUseStartDate;
        
        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="http://tempuri.org/", Order=9)]
        public string strEndDate;
        
        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="http://tempuri.org/", Order=10)]
        public bool bUseEndDate;
        
        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="http://tempuri.org/", Order=11)]
        public string strXML;
        
        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="http://tempuri.org/", Order=12)]
        public long nErrorCode;
        
        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="http://tempuri.org/", Order=13)]
        public string strErrorXML;
        
        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="http://tempuri.org/", Order=14)]
        public int deviceReportDetails;
        
        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="http://tempuri.org/", Order=15)]
        public long ControllerID;
        
        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="http://tempuri.org/", Order=16)]
        public int nLanguage;
        
        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="http://tempuri.org/", Order=17)]
        public long nRecordGroup;
        
        public RunReportDefaultsRequest()
        {
        }
        
        public RunReportDefaultsRequest(
                    ProtegeGXService.Logon L, 
                    int nReportType, 
                    long nTableID, 
                    long nID, 
                    long nSiteID, 
                    long nStart, 
                    long nNumberOfRows, 
                    string strStartDate, 
                    bool bUseStartDate, 
                    string strEndDate, 
                    bool bUseEndDate, 
                    string strXML, 
                    long nErrorCode, 
                    string strErrorXML, 
                    int deviceReportDetails, 
                    long ControllerID, 
                    int nLanguage, 
                    long nRecordGroup)
        {
            this.L = L;
            this.nReportType = nReportType;
            this.nTableID = nTableID;
            this.nID = nID;
            this.nSiteID = nSiteID;
            this.nStart = nStart;
            this.nNumberOfRows = nNumberOfRows;
            this.strStartDate = strStartDate;
            this.bUseStartDate = bUseStartDate;
            this.strEndDate = strEndDate;
            this.bUseEndDate = bUseEndDate;
            this.strXML = strXML;
            this.nErrorCode = nErrorCode;
            this.strErrorXML = strErrorXML;
            this.deviceReportDetails = deviceReportDetails;
            this.ControllerID = ControllerID;
            this.nLanguage = nLanguage;
            this.nRecordGroup = nRecordGroup;
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.2.0-preview1.23462.5")]
    [System.ServiceModel.MessageContractAttribute(WrapperName="RunReportDefaultsResponse", WrapperNamespace="http://tempuri.org/", IsWrapped=true)]
    public partial class RunReportDefaultsResponse
    {
        
        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="http://tempuri.org/", Order=0)]
        public bool RunReportDefaultsResult;
        
        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="http://tempuri.org/", Order=1)]
        public string strXML;
        
        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="http://tempuri.org/", Order=2)]
        public long nErrorCode;
        
        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="http://tempuri.org/", Order=3)]
        public string strErrorXML;
        
        public RunReportDefaultsResponse()
        {
        }
        
        public RunReportDefaultsResponse(bool RunReportDefaultsResult, string strXML, long nErrorCode, string strErrorXML)
        {
            this.RunReportDefaultsResult = RunReportDefaultsResult;
            this.strXML = strXML;
            this.nErrorCode = nErrorCode;
            this.strErrorXML = strErrorXML;
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.2.0-preview1.23462.5")]
    [System.ServiceModel.MessageContractAttribute(WrapperName="RunReport", WrapperNamespace="http://tempuri.org/", IsWrapped=true)]
    public partial class RunReportRequest
    {
        
        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="http://tempuri.org/", Order=0)]
        public ProtegeGXService.Logon L;
        
        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="http://tempuri.org/", Order=1)]
        public int nReportType;
        
        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="http://tempuri.org/", Order=2)]
        public long nTableID;
        
        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="http://tempuri.org/", Order=3)]
        public long nID;
        
        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="http://tempuri.org/", Order=4)]
        public long nSiteID;
        
        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="http://tempuri.org/", Order=5)]
        public long ControllerID;
        
        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="http://tempuri.org/", Order=6)]
        public long nStart;
        
        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="http://tempuri.org/", Order=7)]
        public long nNumberOfRows;
        
        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="http://tempuri.org/", Order=8)]
        public int nLanguage;
        
        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="http://tempuri.org/", Order=9)]
        public long nRecordGroup;
        
        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="http://tempuri.org/", Order=10)]
        public string strStartDate;
        
        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="http://tempuri.org/", Order=11)]
        public bool bUseStartDate;
        
        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="http://tempuri.org/", Order=12)]
        public string strEndDate;
        
        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="http://tempuri.org/", Order=13)]
        public bool bUseEndDate;
        
        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="http://tempuri.org/", Order=14)]
        public string strXML;
        
        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="http://tempuri.org/", Order=15)]
        public long nErrorCode;
        
        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="http://tempuri.org/", Order=16)]
        public string strErrorXML;
        
        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="http://tempuri.org/", Order=17)]
        public int deviceReportDetails;
        
        public RunReportRequest()
        {
        }
        
        public RunReportRequest(
                    ProtegeGXService.Logon L, 
                    int nReportType, 
                    long nTableID, 
                    long nID, 
                    long nSiteID, 
                    long ControllerID, 
                    long nStart, 
                    long nNumberOfRows, 
                    int nLanguage, 
                    long nRecordGroup, 
                    string strStartDate, 
                    bool bUseStartDate, 
                    string strEndDate, 
                    bool bUseEndDate, 
                    string strXML, 
                    long nErrorCode, 
                    string strErrorXML, 
                    int deviceReportDetails)
        {
            this.L = L;
            this.nReportType = nReportType;
            this.nTableID = nTableID;
            this.nID = nID;
            this.nSiteID = nSiteID;
            this.ControllerID = ControllerID;
            this.nStart = nStart;
            this.nNumberOfRows = nNumberOfRows;
            this.nLanguage = nLanguage;
            this.nRecordGroup = nRecordGroup;
            this.strStartDate = strStartDate;
            this.bUseStartDate = bUseStartDate;
            this.strEndDate = strEndDate;
            this.bUseEndDate = bUseEndDate;
            this.strXML = strXML;
            this.nErrorCode = nErrorCode;
            this.strErrorXML = strErrorXML;
            this.deviceReportDetails = deviceReportDetails;
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.2.0-preview1.23462.5")]
    [System.ServiceModel.MessageContractAttribute(WrapperName="RunReportResponse", WrapperNamespace="http://tempuri.org/", IsWrapped=true)]
    public partial class RunReportResponse
    {
        
        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="http://tempuri.org/", Order=0)]
        public bool RunReportResult;
        
        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="http://tempuri.org/", Order=1)]
        public string strXML;
        
        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="http://tempuri.org/", Order=2)]
        public long nErrorCode;
        
        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="http://tempuri.org/", Order=3)]
        public string strErrorXML;
        
        public RunReportResponse()
        {
        }
        
        public RunReportResponse(bool RunReportResult, string strXML, long nErrorCode, string strErrorXML)
        {
            this.RunReportResult = RunReportResult;
            this.strXML = strXML;
            this.nErrorCode = nErrorCode;
            this.strErrorXML = strErrorXML;
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.2.0-preview1.23462.5")]
    [System.ServiceModel.MessageContractAttribute(WrapperName="FindRecords", WrapperNamespace="http://tempuri.org/", IsWrapped=true)]
    public partial class FindRecordsRequest
    {
        
        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="http://tempuri.org/", Order=0)]
        public ProtegeGXService.Logon L;
        
        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="http://tempuri.org/", Order=1)]
        public long nTableID;
        
        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="http://tempuri.org/", Order=2)]
        public long nParent;
        
        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="http://tempuri.org/", Order=3)]
        public long nPage;
        
        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="http://tempuri.org/", Order=4)]
        public long nSortField;
        
        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="http://tempuri.org/", Order=5)]
        public bool bSortDirection;
        
        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="http://tempuri.org/", Order=6)]
        public string strFieldName;
        
        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="http://tempuri.org/", Order=7)]
        public bool bContain;
        
        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="http://tempuri.org/", Order=8)]
        public string strSearchString;
        
        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="http://tempuri.org/", Order=9)]
        public long nMinValue;
        
        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="http://tempuri.org/", Order=10)]
        public long nMaxValue;
        
        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="http://tempuri.org/", Order=11)]
        public System.DateTime startDate;
        
        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="http://tempuri.org/", Order=12)]
        public System.DateTime endDate;
        
        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="http://tempuri.org/", Order=13)]
        public long[] list;
        
        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="http://tempuri.org/", Order=14)]
        public bool bNoMoreRows;
        
        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="http://tempuri.org/", Order=15)]
        public ProtegeGXService.DeviceNames RData;
        
        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="http://tempuri.org/", Order=16)]
        public long nErrorCode;
        
        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="http://tempuri.org/", Order=17)]
        public string strErrorXML;
        
        public FindRecordsRequest()
        {
        }
        
        public FindRecordsRequest(
                    ProtegeGXService.Logon L, 
                    long nTableID, 
                    long nParent, 
                    long nPage, 
                    long nSortField, 
                    bool bSortDirection, 
                    string strFieldName, 
                    bool bContain, 
                    string strSearchString, 
                    long nMinValue, 
                    long nMaxValue, 
                    System.DateTime startDate, 
                    System.DateTime endDate, 
                    long[] list, 
                    bool bNoMoreRows, 
                    ProtegeGXService.DeviceNames RData, 
                    long nErrorCode, 
                    string strErrorXML)
        {
            this.L = L;
            this.nTableID = nTableID;
            this.nParent = nParent;
            this.nPage = nPage;
            this.nSortField = nSortField;
            this.bSortDirection = bSortDirection;
            this.strFieldName = strFieldName;
            this.bContain = bContain;
            this.strSearchString = strSearchString;
            this.nMinValue = nMinValue;
            this.nMaxValue = nMaxValue;
            this.startDate = startDate;
            this.endDate = endDate;
            this.list = list;
            this.bNoMoreRows = bNoMoreRows;
            this.RData = RData;
            this.nErrorCode = nErrorCode;
            this.strErrorXML = strErrorXML;
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.2.0-preview1.23462.5")]
    [System.ServiceModel.MessageContractAttribute(WrapperName="FindRecordsResponse", WrapperNamespace="http://tempuri.org/", IsWrapped=true)]
    public partial class FindRecordsResponse
    {
        
        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="http://tempuri.org/", Order=0)]
        public bool FindRecordsResult;
        
        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="http://tempuri.org/", Order=1)]
        public bool bNoMoreRows;
        
        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="http://tempuri.org/", Order=2)]
        public ProtegeGXService.DeviceNames RData;
        
        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="http://tempuri.org/", Order=3)]
        public long nErrorCode;
        
        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="http://tempuri.org/", Order=4)]
        public string strErrorXML;
        
        public FindRecordsResponse()
        {
        }
        
        public FindRecordsResponse(bool FindRecordsResult, bool bNoMoreRows, ProtegeGXService.DeviceNames RData, long nErrorCode, string strErrorXML)
        {
            this.FindRecordsResult = FindRecordsResult;
            this.bNoMoreRows = bNoMoreRows;
            this.RData = RData;
            this.nErrorCode = nErrorCode;
            this.strErrorXML = strErrorXML;
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.2.0-preview1.23462.5")]
    [System.ServiceModel.MessageContractAttribute(WrapperName="GetImageData", WrapperNamespace="http://tempuri.org/", IsWrapped=true)]
    public partial class GetImageDataRequest
    {
        
        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="http://tempuri.org/", Order=0)]
        public ProtegeGXService.Logon L;
        
        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="http://tempuri.org/", Order=1)]
        public long nSiteID;
        
        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="http://tempuri.org/", Order=2)]
        public long nImageID;
        
        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="http://tempuri.org/", Order=3)]
        public ProtegeGXService.CImageData Data;
        
        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="http://tempuri.org/", Order=4)]
        public long nErrorCode;
        
        public GetImageDataRequest()
        {
        }
        
        public GetImageDataRequest(ProtegeGXService.Logon L, long nSiteID, long nImageID, ProtegeGXService.CImageData Data, long nErrorCode)
        {
            this.L = L;
            this.nSiteID = nSiteID;
            this.nImageID = nImageID;
            this.Data = Data;
            this.nErrorCode = nErrorCode;
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.2.0-preview1.23462.5")]
    [System.ServiceModel.MessageContractAttribute(WrapperName="GetImageDataResponse", WrapperNamespace="http://tempuri.org/", IsWrapped=true)]
    public partial class GetImageDataResponse
    {
        
        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="http://tempuri.org/", Order=0)]
        public bool GetImageDataResult;
        
        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="http://tempuri.org/", Order=1)]
        public ProtegeGXService.CImageData Data;
        
        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="http://tempuri.org/", Order=2)]
        public long nErrorCode;
        
        public GetImageDataResponse()
        {
        }
        
        public GetImageDataResponse(bool GetImageDataResult, ProtegeGXService.CImageData Data, long nErrorCode)
        {
            this.GetImageDataResult = GetImageDataResult;
            this.Data = Data;
            this.nErrorCode = nErrorCode;
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.2.0-preview1.23462.5")]
    [System.ServiceModel.MessageContractAttribute(WrapperName="GetVersion", WrapperNamespace="http://tempuri.org/", IsWrapped=true)]
    public partial class GetVersionRequest
    {
        
        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="http://tempuri.org/", Order=0)]
        public ProtegeGXService.Logon L;
        
        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="http://tempuri.org/", Order=1)]
        public string version;
        
        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="http://tempuri.org/", Order=2)]
        public long nErrorCode;
        
        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="http://tempuri.org/", Order=3)]
        public string strErrorXML;
        
        public GetVersionRequest()
        {
        }
        
        public GetVersionRequest(ProtegeGXService.Logon L, string version, long nErrorCode, string strErrorXML)
        {
            this.L = L;
            this.version = version;
            this.nErrorCode = nErrorCode;
            this.strErrorXML = strErrorXML;
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.2.0-preview1.23462.5")]
    [System.ServiceModel.MessageContractAttribute(WrapperName="GetVersionResponse", WrapperNamespace="http://tempuri.org/", IsWrapped=true)]
    public partial class GetVersionResponse
    {
        
        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="http://tempuri.org/", Order=0)]
        public bool GetVersionResult;
        
        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="http://tempuri.org/", Order=1)]
        public string version;
        
        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="http://tempuri.org/", Order=2)]
        public long nErrorCode;
        
        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="http://tempuri.org/", Order=3)]
        public string strErrorXML;
        
        public GetVersionResponse()
        {
        }
        
        public GetVersionResponse(bool GetVersionResult, string version, long nErrorCode, string strErrorXML)
        {
            this.GetVersionResult = GetVersionResult;
            this.version = version;
            this.nErrorCode = nErrorCode;
            this.strErrorXML = strErrorXML;
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.2.0-preview1.23462.5")]
    [System.ServiceModel.MessageContractAttribute(WrapperName="PrepareEventReport", WrapperNamespace="http://tempuri.org/", IsWrapped=true)]
    public partial class PrepareEventReportRequest
    {
        
        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="http://tempuri.org/", Order=0)]
        public ProtegeGXService.Logon L;
        
        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="http://tempuri.org/", Order=1)]
        public long nSiteID;
        
        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="http://tempuri.org/", Order=2)]
        public long nID;
        
        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="http://tempuri.org/", Order=3)]
        public string strStartDate;
        
        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="http://tempuri.org/", Order=4)]
        public string strEndDate;
        
        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="http://tempuri.org/", Order=5)]
        public System.Nullable<long> nControllerID;
        
        public PrepareEventReportRequest()
        {
        }
        
        public PrepareEventReportRequest(ProtegeGXService.Logon L, long nSiteID, long nID, string strStartDate, string strEndDate, System.Nullable<long> nControllerID)
        {
            this.L = L;
            this.nSiteID = nSiteID;
            this.nID = nID;
            this.strStartDate = strStartDate;
            this.strEndDate = strEndDate;
            this.nControllerID = nControllerID;
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.2.0-preview1.23462.5")]
    [System.ServiceModel.MessageContractAttribute(WrapperName="PrepareEventReportResponse", WrapperNamespace="http://tempuri.org/", IsWrapped=true)]
    public partial class PrepareEventReportResponse
    {
        
        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="http://tempuri.org/", Order=0)]
        public bool PrepareEventReportResult;
        
        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="http://tempuri.org/", Order=1)]
        public string strReportHandle;
        
        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="http://tempuri.org/", Order=2)]
        public long nErrorCode;
        
        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="http://tempuri.org/", Order=3)]
        public string strErrorXML;
        
        public PrepareEventReportResponse()
        {
        }
        
        public PrepareEventReportResponse(bool PrepareEventReportResult, string strReportHandle, long nErrorCode, string strErrorXML)
        {
            this.PrepareEventReportResult = PrepareEventReportResult;
            this.strReportHandle = strReportHandle;
            this.nErrorCode = nErrorCode;
            this.strErrorXML = strErrorXML;
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.2.0-preview1.23462.5")]
    [System.ServiceModel.MessageContractAttribute(WrapperName="ExecuteEventReport", WrapperNamespace="http://tempuri.org/", IsWrapped=true)]
    public partial class ExecuteEventReportRequest
    {
        
        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="http://tempuri.org/", Order=0)]
        public ProtegeGXService.Logon L;
        
        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="http://tempuri.org/", Order=1)]
        public string strReportHandle;
        
        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="http://tempuri.org/", Order=2)]
        public System.Nullable<long> nStartID;
        
        public ExecuteEventReportRequest()
        {
        }
        
        public ExecuteEventReportRequest(ProtegeGXService.Logon L, string strReportHandle, System.Nullable<long> nStartID)
        {
            this.L = L;
            this.strReportHandle = strReportHandle;
            this.nStartID = nStartID;
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.2.0-preview1.23462.5")]
    [System.ServiceModel.MessageContractAttribute(WrapperName="ExecuteEventReportResponse", WrapperNamespace="http://tempuri.org/", IsWrapped=true)]
    public partial class ExecuteEventReportResponse
    {
        
        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="http://tempuri.org/", Order=0)]
        public bool ExecuteEventReportResult;
        
        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="http://tempuri.org/", Order=1)]
        public string strResultHandle;
        
        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="http://tempuri.org/", Order=2)]
        public long nErrorCode;
        
        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="http://tempuri.org/", Order=3)]
        public string strErrorXML;
        
        public ExecuteEventReportResponse()
        {
        }
        
        public ExecuteEventReportResponse(bool ExecuteEventReportResult, string strResultHandle, long nErrorCode, string strErrorXML)
        {
            this.ExecuteEventReportResult = ExecuteEventReportResult;
            this.strResultHandle = strResultHandle;
            this.nErrorCode = nErrorCode;
            this.strErrorXML = strErrorXML;
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.2.0-preview1.23462.5")]
    [System.ServiceModel.MessageContractAttribute(WrapperName="GetEventReportData", WrapperNamespace="http://tempuri.org/", IsWrapped=true)]
    public partial class GetEventReportDataRequest
    {
        
        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="http://tempuri.org/", Order=0)]
        public ProtegeGXService.Logon L;
        
        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="http://tempuri.org/", Order=1)]
        public string strResultHandle;
        
        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="http://tempuri.org/", Order=2)]
        public System.Nullable<long> nNumberOfResults;
        
        public GetEventReportDataRequest()
        {
        }
        
        public GetEventReportDataRequest(ProtegeGXService.Logon L, string strResultHandle, System.Nullable<long> nNumberOfResults)
        {
            this.L = L;
            this.strResultHandle = strResultHandle;
            this.nNumberOfResults = nNumberOfResults;
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.2.0-preview1.23462.5")]
    [System.ServiceModel.MessageContractAttribute(WrapperName="GetEventReportDataResponse", WrapperNamespace="http://tempuri.org/", IsWrapped=true)]
    public partial class GetEventReportDataResponse
    {
        
        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="http://tempuri.org/", Order=0)]
        public bool GetEventReportDataResult;
        
        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="http://tempuri.org/", Order=1)]
        public string DataXML;
        
        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="http://tempuri.org/", Order=2)]
        public ProtegeGXService.EventWindowInformation WindowInfo;
        
        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="http://tempuri.org/", Order=3)]
        public bool bEndOfData;
        
        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="http://tempuri.org/", Order=4)]
        public bool bEndOfEvents;
        
        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="http://tempuri.org/", Order=5)]
        public long nErrorCode;
        
        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="http://tempuri.org/", Order=6)]
        public string strErrorXML;
        
        public GetEventReportDataResponse()
        {
        }
        
        public GetEventReportDataResponse(bool GetEventReportDataResult, string DataXML, ProtegeGXService.EventWindowInformation WindowInfo, bool bEndOfData, bool bEndOfEvents, long nErrorCode, string strErrorXML)
        {
            this.GetEventReportDataResult = GetEventReportDataResult;
            this.DataXML = DataXML;
            this.WindowInfo = WindowInfo;
            this.bEndOfData = bEndOfData;
            this.bEndOfEvents = bEndOfEvents;
            this.nErrorCode = nErrorCode;
            this.strErrorXML = strErrorXML;
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.2.0-preview1.23462.5")]
    [System.ServiceModel.MessageContractAttribute(WrapperName="GetMostRecentEventID", WrapperNamespace="http://tempuri.org/", IsWrapped=true)]
    public partial class GetMostRecentEventIDRequest
    {
        
        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="http://tempuri.org/", Order=0)]
        public ProtegeGXService.Logon L;
        
        public GetMostRecentEventIDRequest()
        {
        }
        
        public GetMostRecentEventIDRequest(ProtegeGXService.Logon L)
        {
            this.L = L;
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.2.0-preview1.23462.5")]
    [System.ServiceModel.MessageContractAttribute(WrapperName="GetMostRecentEventIDResponse", WrapperNamespace="http://tempuri.org/", IsWrapped=true)]
    public partial class GetMostRecentEventIDResponse
    {
        
        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="http://tempuri.org/", Order=0)]
        public bool GetMostRecentEventIDResult;
        
        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="http://tempuri.org/", Order=1)]
        public long nMostRecentEventID;
        
        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="http://tempuri.org/", Order=2)]
        public long nErrorCode;
        
        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="http://tempuri.org/", Order=3)]
        public string strErrorXML;
        
        public GetMostRecentEventIDResponse()
        {
        }
        
        public GetMostRecentEventIDResponse(bool GetMostRecentEventIDResult, long nMostRecentEventID, long nErrorCode, string strErrorXML)
        {
            this.GetMostRecentEventIDResult = GetMostRecentEventIDResult;
            this.nMostRecentEventID = nMostRecentEventID;
            this.nErrorCode = nErrorCode;
            this.strErrorXML = strErrorXML;
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.2.0-preview1.23462.5")]
    [System.ServiceModel.MessageContractAttribute(WrapperName="GetTableSchema", WrapperNamespace="http://tempuri.org/", IsWrapped=true)]
    public partial class GetTableSchemaRequest
    {
        
        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="http://tempuri.org/", Order=0)]
        public ProtegeGXService.Logon L;
        
        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="http://tempuri.org/", Order=1)]
        public long nTableID;
        
        public GetTableSchemaRequest()
        {
        }
        
        public GetTableSchemaRequest(ProtegeGXService.Logon L, long nTableID)
        {
            this.L = L;
            this.nTableID = nTableID;
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.2.0-preview1.23462.5")]
    [System.ServiceModel.MessageContractAttribute(WrapperName="GetTableSchemaResponse", WrapperNamespace="http://tempuri.org/", IsWrapped=true)]
    public partial class GetTableSchemaResponse
    {
        
        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="http://tempuri.org/", Order=0)]
        public bool GetTableSchemaResult;
        
        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="http://tempuri.org/", Order=1)]
        public string strXML;
        
        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="http://tempuri.org/", Order=2)]
        public long nErrorCode;
        
        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="http://tempuri.org/", Order=3)]
        public string strErrorXML;
        
        public GetTableSchemaResponse()
        {
        }
        
        public GetTableSchemaResponse(bool GetTableSchemaResult, string strXML, long nErrorCode, string strErrorXML)
        {
            this.GetTableSchemaResult = GetTableSchemaResult;
            this.strXML = strXML;
            this.nErrorCode = nErrorCode;
            this.strErrorXML = strErrorXML;
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.2.0-preview1.23462.5")]
    [System.ServiceModel.MessageContractAttribute(WrapperName="GetTableIDNameMapping", WrapperNamespace="http://tempuri.org/", IsWrapped=true)]
    public partial class GetTableIDNameMappingRequest
    {
        
        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="http://tempuri.org/", Order=0)]
        public ProtegeGXService.Logon L;
        
        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="http://tempuri.org/", Order=1)]
        public System.Collections.Generic.Dictionary<long, string> tableIDMapping;
        
        public GetTableIDNameMappingRequest()
        {
        }
        
        public GetTableIDNameMappingRequest(ProtegeGXService.Logon L, System.Collections.Generic.Dictionary<long, string> tableIDMapping)
        {
            this.L = L;
            this.tableIDMapping = tableIDMapping;
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.2.0-preview1.23462.5")]
    [System.ServiceModel.MessageContractAttribute(WrapperName="GetTableIDNameMappingResponse", WrapperNamespace="http://tempuri.org/", IsWrapped=true)]
    public partial class GetTableIDNameMappingResponse
    {
        
        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="http://tempuri.org/", Order=0)]
        public bool GetTableIDNameMappingResult;
        
        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="http://tempuri.org/", Order=1)]
        public System.Collections.Generic.Dictionary<long, string> tableIDMapping;
        
        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="http://tempuri.org/", Order=2)]
        public long nErrorCode;
        
        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="http://tempuri.org/", Order=3)]
        public string strErrorXML;
        
        public GetTableIDNameMappingResponse()
        {
        }
        
        public GetTableIDNameMappingResponse(bool GetTableIDNameMappingResult, System.Collections.Generic.Dictionary<long, string> tableIDMapping, long nErrorCode, string strErrorXML)
        {
            this.GetTableIDNameMappingResult = GetTableIDNameMappingResult;
            this.tableIDMapping = tableIDMapping;
            this.nErrorCode = nErrorCode;
            this.strErrorXML = strErrorXML;
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.2.0-preview1.23462.5")]
    [System.ServiceModel.MessageContractAttribute(WrapperName="WhoAmI", WrapperNamespace="http://tempuri.org/", IsWrapped=true)]
    public partial class WhoAmIRequest
    {
        
        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="http://tempuri.org/", Order=0)]
        public ProtegeGXService.Logon L;
        
        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="http://tempuri.org/", Order=1)]
        public string strXML;
        
        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="http://tempuri.org/", Order=2)]
        public long nErrorCode;
        
        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="http://tempuri.org/", Order=3)]
        public string strErrorXML;
        
        public WhoAmIRequest()
        {
        }
        
        public WhoAmIRequest(ProtegeGXService.Logon L, string strXML, long nErrorCode, string strErrorXML)
        {
            this.L = L;
            this.strXML = strXML;
            this.nErrorCode = nErrorCode;
            this.strErrorXML = strErrorXML;
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.2.0-preview1.23462.5")]
    [System.ServiceModel.MessageContractAttribute(WrapperName="WhoAmIResponse", WrapperNamespace="http://tempuri.org/", IsWrapped=true)]
    public partial class WhoAmIResponse
    {
        
        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="http://tempuri.org/", Order=0)]
        public bool WhoAmIResult;
        
        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="http://tempuri.org/", Order=1)]
        public string strXML;
        
        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="http://tempuri.org/", Order=2)]
        public long nErrorCode;
        
        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="http://tempuri.org/", Order=3)]
        public string strErrorXML;
        
        public WhoAmIResponse()
        {
        }
        
        public WhoAmIResponse(bool WhoAmIResult, string strXML, long nErrorCode, string strErrorXML)
        {
            this.WhoAmIResult = WhoAmIResult;
            this.strXML = strXML;
            this.nErrorCode = nErrorCode;
            this.strErrorXML = strErrorXML;
        }
    }
    
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.2.0-preview1.23462.5")]
    public interface IService1Channel : ProtegeGXService.IService1, System.ServiceModel.IClientChannel
    {
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.2.0-preview1.23462.5")]
    public partial class Service1Client : System.ServiceModel.ClientBase<ProtegeGXService.IService1>, ProtegeGXService.IService1
    {
        
        /// <summary>
        /// Implement this partial method to configure the service endpoint.
        /// </summary>
        /// <param name="serviceEndpoint">The endpoint to configure</param>
        /// <param name="clientCredentials">The client credentials</param>
        static partial void ConfigureEndpoint(System.ServiceModel.Description.ServiceEndpoint serviceEndpoint, System.ServiceModel.Description.ClientCredentials clientCredentials);
        
        public Service1Client(EndpointConfiguration endpointConfiguration) : 
                base(Service1Client.GetBindingForEndpoint(endpointConfiguration), Service1Client.GetEndpointAddress(endpointConfiguration))
        {
            this.Endpoint.Name = endpointConfiguration.ToString();
            ConfigureEndpoint(this.Endpoint, this.ClientCredentials);
        }
        
        public Service1Client(EndpointConfiguration endpointConfiguration, string remoteAddress) : 
                base(Service1Client.GetBindingForEndpoint(endpointConfiguration), new System.ServiceModel.EndpointAddress(remoteAddress))
        {
            this.Endpoint.Name = endpointConfiguration.ToString();
            ConfigureEndpoint(this.Endpoint, this.ClientCredentials);
        }
        
        public Service1Client(EndpointConfiguration endpointConfiguration, System.ServiceModel.EndpointAddress remoteAddress) : 
                base(Service1Client.GetBindingForEndpoint(endpointConfiguration), remoteAddress)
        {
            this.Endpoint.Name = endpointConfiguration.ToString();
            ConfigureEndpoint(this.Endpoint, this.ClientCredentials);
        }
        
        public Service1Client(System.ServiceModel.Channels.Binding binding, System.ServiceModel.EndpointAddress remoteAddress) : 
                base(binding, remoteAddress)
        {
        }
        
        public System.Threading.Tasks.Task<ProtegeGXService.AddRecordResponse> AddRecordAsync(ProtegeGXService.AddRecordRequest request)
        {
            return base.Channel.AddRecordAsync(request);
        }
        
        public System.Threading.Tasks.Task<ProtegeGXService.AddRecordSCResponse> AddRecordSCAsync(ProtegeGXService.AddRecordSCRequest request)
        {
            return base.Channel.AddRecordSCAsync(request);
        }
        
        public System.Threading.Tasks.Task<ProtegeGXService.UpdateRecordResponse> UpdateRecordAsync(ProtegeGXService.UpdateRecordRequest request)
        {
            return base.Channel.UpdateRecordAsync(request);
        }
        
        public System.Threading.Tasks.Task<ProtegeGXService.GetRecordResponse> GetRecordAsync(ProtegeGXService.GetRecordRequest request)
        {
            return base.Channel.GetRecordAsync(request);
        }
        
        public System.Threading.Tasks.Task<ProtegeGXService.DeleteRecordResponse> DeleteRecordAsync(ProtegeGXService.DeleteRecordRequest request)
        {
            return base.Channel.DeleteRecordAsync(request);
        }
        
        public System.Threading.Tasks.Task<ProtegeGXService.ListRecordsResponse> ListRecordsAsync(ProtegeGXService.ListRecordsRequest request)
        {
            return base.Channel.ListRecordsAsync(request);
        }
        
        public System.Threading.Tasks.Task<ProtegeGXService.EventReportByRecordGroupResponse> EventReportByRecordGroupAsync(ProtegeGXService.EventReportByRecordGroupRequest request)
        {
            return base.Channel.EventReportByRecordGroupAsync(request);
        }
        
        public System.Threading.Tasks.Task<ProtegeGXService.EventReportByUserResponse> EventReportByUserAsync(ProtegeGXService.EventReportByUserRequest request)
        {
            return base.Channel.EventReportByUserAsync(request);
        }
        
        public System.Threading.Tasks.Task<string> GetCurrentLogonAsync()
        {
            return base.Channel.GetCurrentLogonAsync();
        }
        
        public System.Threading.Tasks.Task<ProtegeGXService.SetLogonResponse> SetLogonAsync(ProtegeGXService.SetLogonRequest request)
        {
            return base.Channel.SetLogonAsync(request);
        }
        
        public System.Threading.Tasks.Task<ProtegeGXService.TestMainDatabaseConnectionResponse> TestMainDatabaseConnectionAsync(ProtegeGXService.TestMainDatabaseConnectionRequest request)
        {
            return base.Channel.TestMainDatabaseConnectionAsync(request);
        }
        
        public System.Threading.Tasks.Task<ProtegeGXService.TestEventDatabaseConnectionResponse> TestEventDatabaseConnectionAsync(ProtegeGXService.TestEventDatabaseConnectionRequest request)
        {
            return base.Channel.TestEventDatabaseConnectionAsync(request);
        }
        
        public System.Threading.Tasks.Task<ProtegeGXService.GetDeviceStatusResponse> GetDeviceStatusAsync(ProtegeGXService.GetDeviceStatusRequest request)
        {
            return base.Channel.GetDeviceStatusAsync(request);
        }
        
        public System.Threading.Tasks.Task<bool> ControlFloorAsync(ProtegeGXService.Logon LD, long nSiteID, long nControllerID, long nElevatorCar, long nFloorIndex, ProtegeGXService.MC_FLOOR_CONTROL Control, long nTime)
        {
            return base.Channel.ControlFloorAsync(LD, nSiteID, nControllerID, nElevatorCar, nFloorIndex, Control, nTime);
        }
        
        public System.Threading.Tasks.Task<bool> ControlPGMAsync(ProtegeGXService.Logon LD, long nSiteID, long nControllerID, long nPGMIndex, ProtegeGXService.MC_PGM_CONTROL Control, long nTime)
        {
            return base.Channel.ControlPGMAsync(LD, nSiteID, nControllerID, nPGMIndex, Control, nTime);
        }
        
        public System.Threading.Tasks.Task<bool> ControlAreaAsync(ProtegeGXService.Logon LD, long nSiteID, long nControllerID, long nAreaIndex, ProtegeGXService.MC_AREA_CONTROL Control)
        {
            return base.Channel.ControlAreaAsync(LD, nSiteID, nControllerID, nAreaIndex, Control);
        }
        
        public System.Threading.Tasks.Task<bool> ControlProgFuncAsync(ProtegeGXService.Logon LD, long nSiteID, long nControllerID, long nDoorIndex, ProtegeGXService.MC_PROGFUNC_CONTROL Control)
        {
            return base.Channel.ControlProgFuncAsync(LD, nSiteID, nControllerID, nDoorIndex, Control);
        }
        
        public System.Threading.Tasks.Task<bool> ControlDoorAsync(ProtegeGXService.Logon LD, long nSiteID, long nControllerID, long nDoorIndex, ProtegeGXService.MC_DOOR_CONTROL Control)
        {
            return base.Channel.ControlDoorAsync(LD, nSiteID, nControllerID, nDoorIndex, Control);
        }
        
        public System.Threading.Tasks.Task<bool> ControlZoneAsync(ProtegeGXService.Logon LD, long nSiteID, long nControllerID, long nZoneIndex, ProtegeGXService.MC_ZONE_CONTROL Control, ProtegeGXService.MC_ZONE_TYPE ZoneType)
        {
            return base.Channel.ControlZoneAsync(LD, nSiteID, nControllerID, nZoneIndex, Control, ZoneType);
        }
        
        public System.Threading.Tasks.Task<bool> ModuleCommandAsync(ProtegeGXService.Logon LD, long nSiteID, long nControllerID, ProtegeGXService.MC_MODULE_COMMAND Command, ProtegeGXService.MC_MODULE_COMMAND_TYPE Type, long nModuleAddress)
        {
            return base.Channel.ModuleCommandAsync(LD, nSiteID, nControllerID, Command, Type, nModuleAddress);
        }
        
        public System.Threading.Tasks.Task<bool> ControlServiceAsync(ProtegeGXService.Logon LD, long nSiteID, long nControllerID, long nServiceType, long nIndex, ProtegeGXService.MC_SERVICE_CONTROL Control)
        {
            return base.Channel.ControlServiceAsync(LD, nSiteID, nControllerID, nServiceType, nIndex, Control);
        }
        
        public System.Threading.Tasks.Task<ProtegeGXService.SetDateTimeResponse> SetDateTimeAsync(ProtegeGXService.SetDateTimeRequest request)
        {
            return base.Channel.SetDateTimeAsync(request);
        }
        
        public System.Threading.Tasks.Task<ProtegeGXService.InitialiseKabaKeyResponse> InitialiseKabaKeyAsync(ProtegeGXService.InitialiseKabaKeyRequest request)
        {
            return base.Channel.InitialiseKabaKeyAsync(request);
        }
        
        public System.Threading.Tasks.Task<ProtegeGXService.ControlDeviceResponse> ControlDeviceAsync(ProtegeGXService.ControlDeviceRequest request)
        {
            return base.Channel.ControlDeviceAsync(request);
        }
        
        public System.Threading.Tasks.Task<ProtegeGXService.ResetPasswordResponse> ResetPasswordAsync(ProtegeGXService.ResetPasswordRequest request)
        {
            return base.Channel.ResetPasswordAsync(request);
        }
        
        public System.Threading.Tasks.Task<ProtegeGXService.GetStatusListStatusResponse> GetStatusListStatusAsync(ProtegeGXService.GetStatusListStatusRequest request)
        {
            return base.Channel.GetStatusListStatusAsync(request);
        }
        
        public System.Threading.Tasks.Task<ProtegeGXService.GetLatestEventsResponse> GetLatestEventsAsync(ProtegeGXService.GetLatestEventsRequest request)
        {
            return base.Channel.GetLatestEventsAsync(request);
        }
        
        public System.Threading.Tasks.Task<ProtegeGXService.RunReportDefaultsResponse> RunReportDefaultsAsync(ProtegeGXService.RunReportDefaultsRequest request)
        {
            return base.Channel.RunReportDefaultsAsync(request);
        }
        
        public System.Threading.Tasks.Task<ProtegeGXService.RunReportResponse> RunReportAsync(ProtegeGXService.RunReportRequest request)
        {
            return base.Channel.RunReportAsync(request);
        }
        
        public System.Threading.Tasks.Task<ProtegeGXService.FindRecordsResponse> FindRecordsAsync(ProtegeGXService.FindRecordsRequest request)
        {
            return base.Channel.FindRecordsAsync(request);
        }
        
        public System.Threading.Tasks.Task<ProtegeGXService.GetImageDataResponse> GetImageDataAsync(ProtegeGXService.GetImageDataRequest request)
        {
            return base.Channel.GetImageDataAsync(request);
        }
        
        public System.Threading.Tasks.Task<ProtegeGXService.GetVersionResponse> GetVersionAsync(ProtegeGXService.GetVersionRequest request)
        {
            return base.Channel.GetVersionAsync(request);
        }
        
        public System.Threading.Tasks.Task<ProtegeGXService.PrepareEventReportResponse> PrepareEventReportAsync(ProtegeGXService.PrepareEventReportRequest request)
        {
            return base.Channel.PrepareEventReportAsync(request);
        }
        
        public System.Threading.Tasks.Task<ProtegeGXService.ExecuteEventReportResponse> ExecuteEventReportAsync(ProtegeGXService.ExecuteEventReportRequest request)
        {
            return base.Channel.ExecuteEventReportAsync(request);
        }
        
        public System.Threading.Tasks.Task<ProtegeGXService.GetEventReportDataResponse> GetEventReportDataAsync(ProtegeGXService.GetEventReportDataRequest request)
        {
            return base.Channel.GetEventReportDataAsync(request);
        }
        
        public System.Threading.Tasks.Task<ProtegeGXService.GetMostRecentEventIDResponse> GetMostRecentEventIDAsync(ProtegeGXService.GetMostRecentEventIDRequest request)
        {
            return base.Channel.GetMostRecentEventIDAsync(request);
        }
        
        public System.Threading.Tasks.Task<ProtegeGXService.GetTableSchemaResponse> GetTableSchemaAsync(ProtegeGXService.GetTableSchemaRequest request)
        {
            return base.Channel.GetTableSchemaAsync(request);
        }
        
        public System.Threading.Tasks.Task<ProtegeGXService.GetTableIDNameMappingResponse> GetTableIDNameMappingAsync(ProtegeGXService.GetTableIDNameMappingRequest request)
        {
            return base.Channel.GetTableIDNameMappingAsync(request);
        }
        
        public System.Threading.Tasks.Task<ProtegeGXService.WhoAmIResponse> WhoAmIAsync(ProtegeGXService.WhoAmIRequest request)
        {
            return base.Channel.WhoAmIAsync(request);
        }
        
        public virtual System.Threading.Tasks.Task OpenAsync()
        {
            return System.Threading.Tasks.Task.Factory.FromAsync(((System.ServiceModel.ICommunicationObject)(this)).BeginOpen(null, null), new System.Action<System.IAsyncResult>(((System.ServiceModel.ICommunicationObject)(this)).EndOpen));
        }
        
        private static System.ServiceModel.Channels.Binding GetBindingForEndpoint(EndpointConfiguration endpointConfiguration)
        {
            if ((endpointConfiguration == EndpointConfiguration.BasicHttpBinding_IService1))
            {
                System.ServiceModel.BasicHttpBinding result = new System.ServiceModel.BasicHttpBinding();
                result.MaxBufferSize = int.MaxValue;
                result.ReaderQuotas = System.Xml.XmlDictionaryReaderQuotas.Max;
                result.MaxReceivedMessageSize = int.MaxValue;
                result.AllowCookies = true;
                return result;
            }
            if ((endpointConfiguration == EndpointConfiguration.BasicHttpBinding_IService11))
            {
                System.ServiceModel.BasicHttpBinding result = new System.ServiceModel.BasicHttpBinding();
                result.MaxBufferSize = int.MaxValue;
                result.ReaderQuotas = System.Xml.XmlDictionaryReaderQuotas.Max;
                result.MaxReceivedMessageSize = int.MaxValue;
                result.AllowCookies = true;
                result.Security.Mode = System.ServiceModel.BasicHttpSecurityMode.Transport;
                return result;
            }
            throw new System.InvalidOperationException(string.Format("Could not find endpoint with name \'{0}\'.", endpointConfiguration));
        }
        
        private static System.ServiceModel.EndpointAddress GetEndpointAddress(EndpointConfiguration endpointConfiguration)
        {
            if ((endpointConfiguration == EndpointConfiguration.BasicHttpBinding_IService1))
            {
                return new System.ServiceModel.EndpointAddress("http://vmgx01.polyexpert.com:8030/ProtegeGXSOAPService/Service.svc");
            }
            if ((endpointConfiguration == EndpointConfiguration.BasicHttpBinding_IService11))
            {
                return new System.ServiceModel.EndpointAddress("https://vmgx01.polyexpert.com:8040/ProtegeGXSOAPService/Service.svc");
            }
            throw new System.InvalidOperationException(string.Format("Could not find endpoint with name \'{0}\'.", endpointConfiguration));
        }
        
        public enum EndpointConfiguration
        {
            
            BasicHttpBinding_IService1,
            
            BasicHttpBinding_IService11,
        }
    }
}
