﻿@using System.Net.Http
@using System.Net.Http.Json
@using Microsoft.AspNetCore.Components.Forms
@using Microsoft.AspNetCore.Components.Routing
@using Microsoft.AspNetCore.Components.Web
@using static Microsoft.AspNetCore.Components.Web.RenderMode
@using Microsoft.AspNetCore.Components.Web.Virtualization
@using Microsoft.JSInterop
@using VisitLog3
@using VisitLog3.Components
@using Microsoft.AspNetCore.Authorization
@using Microsoft.AspNetCore.Components.Authorization
@using Blazored.SessionStorage
@using MudBlazor
@using VisitLog3.Models
@using ExcelCSVExport.Helpers;
@using VisitLog3.Helpers;
@using ExcelCSVExport.Enums;
@using Microsoft.AspNetCore.Antiforgery;
@using Mobsites.Blazor
@using PetaPoco
@using PetaPoco.Providers
@using Microsoft.AspNetCore.Authentication;
@using ProtegeGXService;
@using ReactorBlazorQRCodeScanner
@using Microsoft.AspNetCore.Components.Server.ProtectedBrowserStorage



@using VisitLog3.Services;
@inject SerAppSession serApp

