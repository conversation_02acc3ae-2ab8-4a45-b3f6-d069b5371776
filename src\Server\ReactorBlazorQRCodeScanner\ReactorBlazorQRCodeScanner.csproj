﻿<Project Sdk="Microsoft.NET.Sdk.Razor">

  <PropertyGroup>
    <TargetFramework>net9.0</TargetFramework>
    <Nullable>enable</Nullable>
    <ImplicitUsings>enable</ImplicitUsings>

    <PackageId>ReactorBlazorQRCodeScanner</PackageId>
    <Version>1.0.0</Version>
    <Authors><PERSON><PERSON></Authors>
    <Company>reactor.fr</Company>
    <PackageTags>Blazor;Reactor;QRCode;ScannerQR Code</PackageTags>

    <PackageReadmeFile>README.md</PackageReadmeFile>
    <PackageLicenseFile>LICENSE.txt</PackageLicenseFile>
  </PropertyGroup>

  <ItemGroup>     
    <None Include="README.md" Pack="true" PackagePath="\" /> <!--Nécessaire pour nuget package-->
    <None Include="LICENSE.txt" Pack="true" PackagePath="\" />
  </ItemGroup>
  
  <ItemGroup>
    <SupportedPlatform Include="browser" />
  </ItemGroup>

  <ItemGroup>
    <PackageReference Include="Microsoft.AspNetCore.Components.Web" Version="9.0.0" />
  </ItemGroup>

</Project>
