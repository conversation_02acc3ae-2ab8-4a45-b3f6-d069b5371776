﻿@using System.Security.Claims
@using Microsoft.AspNetCore.Authentication
@inject NavigationManager NavigationManager

<div>
    <form method="post" action="Account/PerformMicrosoftLogin">
        <AntiforgeryToken />
        <input type="hidden" name="returnUrl" value="@NavigationManager.Uri" />
        <button class="secondary-btn d-flex align-items-center justify-content-center py-2 px-4 position-fixed top-0 end-0 m-3" aria-label="Log in with Microsoft">
            <span style="margin-right: 8px;">Log in with Microsoft</span>
            <img src="/images/right-to-bracket-solid.png" alt="Microsoft Icon" style="width: 20px; height: 20px;">
        </button>

    </form>
</div>