﻿using Microsoft.AspNetCore.Mvc;
using ProtegeGXService;
using System.ServiceModel;
using System.Xml.Linq;
using VisitLog3.Helpers;

namespace VisitLog3.Controllers
{
    public class LocationController : Controller
    {
        [HttpGet("/api/clc")]
        public IActionResult CreateLocationCookie([FromQuery] string buttonType)
        {
            CookieOptions co = new()
            {
                Expires = DateTime.UtcNow.AddYears(10),
                SameSite = SameSiteMode.Strict,
                Secure = true
            };

            Response.Cookies.Append("location", buttonType, co);
            return Redirect("/");
        }

        [HttpGet("/api/clearLocationCookie")]
        public IActionResult ClearLocationCookie([FromQuery] string buttonType)
        {
            Response.Cookies.Delete("location");

            return Redirect("/");
        }

    }

}
