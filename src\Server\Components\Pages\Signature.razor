﻿@page "/Signature"
@rendermode InteractiveServer

@inject HttpClient Http
@inject ISnackbar Snackbar
@inject IConfiguration Configuration
@inject NavigationManager Navigation
@inject Blazored.SessionStorage.ISessionStorageService SessionStorage
@inject ProtectedSessionStorage ProtectedSessionStorage
@inject IHttpContextAccessor HttpContextAccessor
@using System.Text.Json

<div class="container top-16">
    <div class="col-7 custom-offset">
        <div class="d-grid gap-5">
            <div class="d-flex justify-content-between align-items-center py-3">
                <!-- Back Button -->
                <button class="secondary-btn d-flex align-items-center justify-content-center py-2 px-4" aria-label="Go Back" @onclick="NavigateBack">
                    <img src="/images/arrow-left-solid.png" alt="House Icon" style="width: 20px; height: 20px;">
                </button>

                <!-- Header Text -->
                <h4 class="primary-header text-center my-0">
                    @GetLocalizedText("Veuillez signer", "Please sign")
                </h4>

                <!-- Home Button -->
                <button class="secondary-btn d-flex align-items-center justify-content-center py-2 px-4" aria-label="Go to Home" @onclick="NavigateToAccueil">
                    <img src="/images/house-white.png" alt="House Icon" style="width: 20px; height: 20px;">
                </button>
            </div>

        </div>
        <div class="signature-container">
            <SignaturePad @ref="signaturePad" OnSignatureChange="OnSignatureChange" Style="width:100%" BackgroundImage="/eraser-solid.png">
                <SignaturePadFooter>
                    <SignaturePadClear >
                        <button id="signature-pad--clear" class="secondary-btn float-end px-4 py-2">
                            <img src="/images/eraser-solid.png" alt="House Icon" style="width: 20px; height: 20px;">
                        </button>
                    </SignaturePadClear>
                </SignaturePadFooter>
            </SignaturePad>

        </div>
        <button disabled="@string.IsNullOrEmpty(dataURL)" style="margin-top: 75px;" class="secondary-btn float-end w-100 py-2" @onclick="Next">
            <img src="/images/arrow-right-solid.png" alt="House Icon" style="width: 20px; height: 20px;">
        </button>
    </div>
</div>


@code {
    private bool isInitialized = false;
    private string lng = "";
    SignaturePad signaturePad;
    SignaturePad.SupportedSaveAsTypes signatureType = SignaturePad.SupportedSaveAsTypes.png;
    string dataURL;

    protected override async Task OnAfterRenderAsync(bool firstRender)
    {
        if (firstRender)
        {

            lng = await SessionStorage.GetItemAsync<string>("lng") ?? "en";

            isInitialized = true;
            StateHasChanged();
        }
    }


    private string GetLocalizedText(string textFr, string textEn)
    {
        return lng == "en" ? textEn : textFr;
    }

    private async Task OnSignatureChange()
    {
        dataURL = await signaturePad.ToDataURL(signatureType);
    }

    private async Task NavigateBack()
    {
        // 1. Get JSON string (could be null or empty if not set)
        var selectedAreaJson = await SessionStorage.GetItemAsync<string>("selectedArea");

        // 2. If it’s empty or null, navigate to /Accueil
        if (string.IsNullOrEmpty(selectedAreaJson))
        {
            Navigation.NavigateTo("/Accueil");
            return;
        }

        try
        {
            // 3. Deserialize the JSON back into a Lieu object
            var lieu = JsonSerializer.Deserialize<Lieu>(selectedAreaJson);

            if (lieu?.requires_SQF == true)
            {
                Navigation.NavigateTo("/SQF");
            }
            else
            {
                var scanCode = await SessionStorage.GetItemAsync<string>("scanCode");

                if (!string.IsNullOrWhiteSpace(scanCode))
                {
                    Navigation.NavigateTo("/Identification");
                }
                else
                {
                    Navigation.NavigateTo("/Lieux");
                }
            }

        }
        catch (JsonException)
        {
            // If JSON is invalid or corrupted, fallback
            Navigation.NavigateTo("/Accueil");
        }
    }

    private async Task NavigateToAccueil()
    {
        Navigation.NavigateTo("/");
    }

    private async Task Next()
    {
        await ProtectedSessionStorage.SetAsync("base64Signature", dataURL);
        await SessionStorage.SetItemAsync("departure", false);

        var scanCode = await SessionStorage.GetItemAsync<string>("scanCode");
        if (string.IsNullOrWhiteSpace(scanCode))
        {
            // No card yet → go scan
            Navigation.NavigateTo("/Scan");
            return;
        }

        var selectedArea = await SessionStorage.GetItemAsync<Lieu>("selectedArea");
        var contact = await SessionStorage.GetItemAsync<Contact>("selectedContact");

        var payload = new ScanRequestModel
            {
                ScanResult = scanCode,
                telephone = await SessionStorage.GetItemAsync<string>("telephone"),
                Prenom = await SessionStorage.GetItemAsync<string>("prenom"),
                Nom = await SessionStorage.GetItemAsync<string>("nom"),
                Compagnie = await SessionStorage.GetItemAsync<string>("compagnie"),
                Base64Signature = dataURL,
                Lng = await SessionStorage.GetItemAsync<string>("lng") ?? "en",
                SelectedArea = selectedArea?.id_lieux ?? 0,
                ContactID = contact?.ID_EMPLOYE ?? -1,
                ContactEmail = contact?.EMAIL,
                ContactName = contact != null
                                  ? $"{contact.NOM}{(string.IsNullOrWhiteSpace(contact.PRENOM) ? "" : " " + contact.PRENOM)}"
                                  : null,
                EntryPoint = HttpContextAccessor.HttpContext?.Request.Cookies["location"]
            };

        try
        {
            // 1 – simplest
            var apiUrl = $"{Navigation.BaseUri}api/scan/entry";

            // Build the request without extra headers
            var req = new HttpRequestMessage(HttpMethod.Post, apiUrl)
                {
                    Content = JsonContent.Create(payload)
                };

            var resp = await Http.SendAsync(req);

            if (resp.IsSuccessStatusCode)
            {
                Snackbar.Configuration.PositionClass = Defaults.Classes.Position.BottomRight;
                Snackbar.Add(payload.Lng == "en"
                              ? "Entry recorded successfully!"
                              : "Arrivée enregistrée !", Severity.Success);

                Navigation.NavigateTo("/");
            }
            else
            {
                var apiErr = await resp.Content.ReadFromJsonAsync<ApiError>();
                Snackbar.Configuration.PositionClass = Defaults.Classes.Position.BottomRight;
                Snackbar.Add(apiErr?.message ?? "API error", Severity.Error);
            }
        }
        catch (Exception ex)
        {
            Snackbar.Add($"Network error: {ex.Message}", Severity.Error);
        }

    }

    // Local helper for error JSON
    private sealed class ApiError { public string? message { get; set; } }
}
