.mud-popover {
    animation: cssAnimation 0s 0.2s forwards;
    visibility: hidden;
}

@keyframes cssAnimation {
    to {
        visibility: visible;
    }
}

html, body {
    font-family: 'Helvetica Neue', Helvetica, Arial, sans-serif;
}

a, .btn-link {
    color: #006bb7;
}

.btn-primary {
    color: #fff;
    background-color: #1b6ec2;
    border-color: #1861ac;
}

.btn:focus, .btn:active:focus, .btn-link.nav-link:focus, .form-control:focus, .form-check-input:focus {
    box-shadow: 0 0 0 0.1rem white, 0 0 0 0.25rem #258cfb;
}

.mud-popover {
    animation: cssAnimation 0s 0.2s forwards;
}

.content {
    padding-top: 1.1rem;
}

h1:focus {
    outline: none;
}

.valid.modified:not([type=checkbox]) {
    outline: 1px solid #26b050;
}

.invalid {
    outline: 1px solid #e50000;
}

.validation-message {
    color: #e50000;
}

.blazor-error-boundary {
    background: url(data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNTYiIGhlaWdodD0iNDkiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyIgeG1sbnM6eGxpbms9Imh0dHA6Ly93d3cudzMub3JnLzE5OTkveGxpbmsiIG92ZXJmbG93PSJoaWRkZW4iPjxkZWZzPjxjbGlwUGF0aCBpZD0iY2xpcDAiPjxyZWN0IHg9IjIzNSIgeT0iNTEiIHdpZHRoPSI1NiIgaGVpZ2h0PSI0OSIvPjwvY2xpcFBhdGg+PC9kZWZzPjxnIGNsaXAtcGF0aD0idXJsKCNjbGlwMCkiIHRyYW5zZm9ybT0idHJhbnNsYXRlKC0yMzUgLTUxKSI+PHBhdGggZD0iTTI2My41MDYgNTFDMjY0LjcxNyA1MSAyNjUuODEzIDUxLjQ4MzcgMjY2LjYwNiA1Mi4yNjU4TDI2Ny4wNTIgNTIuNzk4NyAyNjcuNTM5IDUzLjYyODMgMjkwLjE4NSA5Mi4xODMxIDI5MC41NDUgOTIuNzk1IDI5MC42NTYgOTIuOTk2QzI5MC44NzcgOTMuNTEzIDI5MSA5NC4wODE1IDI5MSA5NC42NzgyIDI5MSA5Ny4wNjUxIDI4OS4wMzggOTkgMjg2LjYxNyA5OUwyNDAuMzgzIDk5QzIzNy45NjMgOTkgMjM2IDk3LjA2NTEgMjM2IDk0LjY3ODIgMjM2IDk0LjM3OTkgMjM2LjAzMSA5NC4wODg2IDIzNi4wODkgOTMuODA3MkwyMzYuMzM4IDkzLjAxNjIgMjM2Ljg1OCA5Mi4xMzE0IDI1OS40NzMgNTMuNjI5NCAyNTkuOTYxIDUyLjc5ODUgMjYwLjQwNyA1Mi4yNjU4QzI2MS4yIDUxLjQ4MzcgMjYyLjI5NiA1MSAyNjMuNTA2IDUxWk0yNjMuNTg2IDY2LjAxODNDMjYwLjczNyA2Ni4wMTgzIDI1OS4zMTMgNjcuMTI0NSAyNTkuMzEzIDY5LjMzNyAyNTkuMzEzIDY5LjYxMDIgMjU5LjMzMiA2OS44NjA4IDI1OS4zNzEgNzAuMDg4N0wyNjEuNzk1IDg0LjAxNjEgMjY1LjM4IDg0LjAxNjEgMjY3LjgyMSA2OS43NDc1QzI2Ny44NiA2OS43MzA5IDI2Ny44NzkgNjkuNTg3NyAyNjcuODc5IDY5LjMxNzkgMjY3Ljg3OSA2Ny4xMTgyIDI2Ni40NDggNjYuMDE4MyAyNjMuNTg2IDY2LjAxODNaTTI2My41NzYgODYuMDU0N0MyNjEuMDQ5IDg2LjA1NDcgMjU5Ljc4NiA4Ny4zMDA1IDI1OS43ODYgODkuNzkyMSAyNTkuNzg2IDkyLjI4MzcgMjYxLjA0OSA5My41Mjk1IDI2My41NzYgOTMuNTI5NSAyNjYuMTE2IDkzLjUyOTUgMjY3LjM4NyA5Mi4yODM3IDI2Ny4zODcgODkuNzkyMSAyNjcuMzg3IDg3LjMwMDUgMjY2LjExNiA4Ni4wNTQ3IDI2My41NzYgODYuMDU0N1oiIGZpbGw9IiNGRkU1MDAiIGZpbGwtcnVsZT0iZXZlbm9kZCIvPjwvZz48L3N2Zz4=) no-repeat 1rem/1.8rem, #b32121;
    padding: 1rem 1rem 1rem 3.7rem;
    color: white;
}

    .blazor-error-boundary::after {
        content: "An error has occurred."
    }

.darker-border-checkbox.form-check-input {
    border-color: #929292;
}



html {
    font-size: 14px;
}

@media (min-width: 768px) {
    html {
        font-size: 16px;
    }
}

.btn:focus, .btn:active:focus, .btn-link.nav-link:focus, .form-control:focus, .form-check-input:focus {
    box-shadow: 0 0 0 0.1rem white, 0 0 0 0.25rem #258cfb;
}

html {
    position: relative;
    min-height: 100%;
}

body {
    margin-bottom: 60px;
}

.primary-btn {
    display: block;
    padding: 20px 40px;
    font-size: 1.5rem;
    font-weight: bold;
    border-radius: 8px;
    border: 2px solid rgb(136, 155, 199);
    background-color: rgb(250, 250, 255);
    color: rgb(101, 115, 147);
    text-align: center;
    font-family: 'Roboto', sans-serif;
}

.primary-header {
    color: rgb(101, 115, 147);
    font-family: 'Roboto', sans-serif;
}

.secondary-btn {
    display: block;
    padding: 20px 40px;
    font-size: 1rem;
    font-weight: bold;
    border-radius: 8px;
    border: none;
    background-color: rgb(136, 155, 199);
    color: white;
    text-align: center;
    font-family: 'Roboto', sans-serif;
}

.secondary-btn:hover {
    color: white !important; /* Keeps the original text color */
}


.mt-6 {
    margin-top: 13rem;
}

.primary-input {
    width: 100% !important;
    padding: 10px !important;
    font-size: 1rem !important;
    border: 2px solid rgb(136, 155, 199) !important;
    border-radius: 5px !important;
    color: rgb(81, 92, 118) !important;
}

    .primary-input:focus {
        outline: none;
        border-color: black;
        box-shadow: 0 0 5px rgba(0, 86, 179, 0.5);
    }

.primary-label {
    color: rgb(81, 92, 118);
}

.spinner {
    border-top: 4px solid rgb(101, 115, 147);
    border-radius: 50%;
    width: 50px;
    height: 50px;
    animation: spin 0.6s linear infinite;
}

@keyframes spin {
    0% {
        transform: rotate(0deg);
    }

    100% {
        transform: rotate(360deg);
    }
}

.top-20 {
    top: 20%;
}

.top-30 {
    top: 30%;
}

.top-40 {
    top: 40%;
}

.top-16 {
    margin-top: 2%;
}

.input-validation-error {
    border: 2px solid red;
}

datalist option {
    width: 100%;
}

.autoComplete_wrapper {
    width: 100% !important;
}

.custom-popover {
    max-height: 200px !important;
}

.mud-input-control-helper-container {
    display: none !important;
}

.sqf-checkbox p {
    font-size: 0.9rem;
}

.mud-input-control {
    padding-left: 3px !important;
    padding-right: 3px !important;
}

.mud-checkbox .mud-button-root {
    padding: 0px !important;
    margin-right: 10px !important;
    margin-top: 10px !important;
    margin-bottom: 10px !important;
}


.custom-offset {
    margin-left: 44%;
}


.secondary-btn:disabled {
    background-color: #cccccc;
}


.singaturePadClass {
    border: solid;
    height: 150px;
    width: 100%;
    border-color: rgb(81, 92, 118);
    border-radius: 8px;
}

.signature-container {
    margin-top: 50px;
    width: 100%;
    position: relative;
}

.clear-button {
    position: absolute;
    top: 10px;
    right: 10px;
}

.last-two-spacing {
    margin-top: 20px !important;
}

.admin-grid-header {
    font-weight: bold !important;
}

.adminGrid td {
    padding-left: 4px !important;
    padding-right: 2px !important;
    padding-top: 2px !important;
    padding-bottom: 2px !important;
}

.adminGrid th {
    padding-left: 6px !important;
    padding-right: 2px !important;
    padding-top: 2px !important;
    padding-bottom: 4px !important;
}

body {
    background-image: url('/images/pex_background.png'); /* Path to your image */
    background-color: rgba(255, 255, 255, 0.486);
    background-repeat: no-repeat;
    background-attachment: fixed;
    background-size: contain;
}

.scanner-container {
    position: relative;
    display: inline-block;
}

.overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5); /* Darkens the video */
    pointer-events: none; /* Allow interactions */
}

.laser-image {
    position: absolute;
    top: 50%; /* Center vertically */
    left: 50%; /* Center horizontally */
    transform: translate(-50%, -50%); /* Perfect centering */
    width: 80%; /* Adjust the size as needed */
    max-width: 500px;
    opacity: 0.7; /* Make it slightly transparent */
    pointer-events: none; /* Ensure it doesn't block scanning */
}

#mud-snackbar-container {
    width:500px !important;
    font-size:25px !important;
}
