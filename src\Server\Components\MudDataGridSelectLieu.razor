﻿<MudSelect T="int?" Value="Position" ValueChanged="HandleValueChanged" Margin="@Margin.Dense">
    @ChildContent
</MudSelect>

@code {
    [Parameter]
    public RenderFragment? ChildContent { get; set; }

    [Parameter]
    [EditorRequired]
    public CellContext<Visiteur_Log> Context { get; set; } = default!;

    [CascadingParameter]
    public MudDataGrid<Visiteur_Log> DataGrid { get; set; } = default!;

    private int? Position
    {
        get => Context.Item.id_lieux;

    }

    private async Task HandleValueChanged(int? newPosition)
    {
        Context.Item.id_lieux = newPosition;
        await DataGrid.CommittedItemChanges.InvokeAsync(Context.Item);
    }
}
