﻿@page "/Lieux"
@rendermode InteractiveServer
@inject IConfiguration Configuration
@inject NavigationManager Navigation
@inject Blazored.SessionStorage.ISessionStorageService SessionStorage

<div class="container top-16 ">
    <div class="col-7 custom-offset">
        <div class="d-grid gap-5">
            <div class="d-flex justify-content-between align-items-center py-3">
                <!-- Back Button -->
                <button class="secondary-btn d-flex align-items-center justify-content-center py-2 px-4" aria-label="Go Back" @onclick="NavigateBack">
                    <img src="/images/arrow-left-solid.png" alt="House Icon" style="width: 20px; height: 20px;">
                </button>

                <!-- Header Text -->
                <h4 class="primary-header text-center my-0">
                    @GetLocalizedText("Où allez-vous circuler?", "Which areas do you need access to?")
                </h4>

                <!-- Home Button -->
                <button class="secondary-btn d-flex align-items-center justify-content-center py-2 px-4" aria-label="Go to Home" @onclick="NavigateToAccueil">
                    <img src="/images/house-white.png" alt="House Icon" style="width: 20px; height: 20px;">
                </button>
            </div>
            <div class="d-grid gap-5" style="margin-top:120px">
                @foreach (var lieu in lieux)
                {
                    <ButtonLieu Lieu="lieu">
                        @if (lng == "en")
                        {
                            @lieu.nom_en
                        }
                        else
                        {
                            @lieu.nom_fr
                        }
                    </ButtonLieu>
                }
            </div>
        </div>
    </div>
</div>

@code {

    [Inject]
    IHttpContextAccessor HttpContextAccessor { get; set; }


    private string lng = "en";
    private List<Lieu> lieux = new();
    private bool isInitialized = false;

    protected override async Task OnInitializedAsync()
    {
        var token = HttpContextAccessor.HttpContext.Request.Cookies["Location"];

        var connectionString = Configuration.GetConnectionString("dbInformatique");
        using var db = new PetaPoco.Database(connectionString, "System.Data.SqlClient");

        string sql = @"
        SELECT l.id_lieux,
               l.nom_fr,
               l.requires_SQF,
               l.nom_en,
               i.config
        FROM [Informatique].[dbo].[Config_Lieu] i
        JOIN [Informatique].[dbo].[Lieux] l
            ON i.id_lieu = l.id_lieux
        WHERE i.config = @0";

        lieux = db.Fetch<Lieu>(sql, token);

        // If no lieux were found for the IP address, fetch all lieux from the database
        if (lieux == null || !lieux.Any())
        {
            string allLieuxSql = @"
            SELECT l.id_lieux,
                   l.nom_fr,
                   l.requires_SQF,
                   l.nom_en
            FROM [Informatique].[dbo].[Lieux] l";

            lieux = db.Fetch<Lieu>(allLieuxSql);
        }
    }

    protected override async Task OnAfterRenderAsync(bool firstRender)
    {
        if (firstRender)
        {
            // Get language preference from session storage
            lng = await SessionStorage.GetItemAsync<string>("lng") ?? "en";

            isInitialized = true;
            StateHasChanged();
        }
    }

    private string GetLocalizedText(string textFr, string textEn)
    {
        return lng == "en" ? textEn : textFr;
    }

    private async Task NavigateBack()
    {
        Navigation.NavigateTo("/Identification");
    }

    private async Task NavigateToAccueil()
    {
        Navigation.NavigateTo("/");
    }

}
