{"$schema": "http://json.schemastore.org/launchsettings.json", "iisSettings": {"windowsAuthentication": false, "anonymousAuthentication": true, "iisExpress": {"applicationUrl": "http://localhost:19589", "sslPort": 44365}}, "profiles": {"http": {"commandName": "Project", "dotnetRunMessages": true, "launchBrowser": true, "applicationUrl": "http://localhost:5115", "environmentVariables": {"ASPNETCORE_ENVIRONMENT": "Development"}}, "https": {"commandName": "Project", "dotnetRunMessages": true, "launchBrowser": true, "applicationUrl": "https://localhost:7130;http://localhost:5115", "environmentVariables": {"ASPNETCORE_ENVIRONMENT": "Development"}}, "IIS Express": {"commandName": "IISExpress", "launchBrowser": true, "environmentVariables": {"ASPNETCORE_ENVIRONMENT": "Development"}}}}