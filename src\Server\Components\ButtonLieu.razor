﻿@inject IConfiguration Configuration
@inject NavigationManager Navigation
@inject Blazored.SessionStorage.ISessionStorageService SessionStorage

<button class="@ButtonClass" style="@ButtonStyle" @onclick="selectArea">
    @ChildContent
</button>

@code {
    [Parameter]
    public Lieu Lieu { get; set; } = default!;

    [Parameter]
    public string ButtonClass { get; set; } = "primary-btn btn-lg p-3 fw-bold fs-3";

    [Parameter]
    public string ButtonStyle { get; set; } = "width: 70%; margin: 0 auto;";

    [Parameter]
    public EventCallback OnClick { get; set; }

    [Parameter]
    public RenderFragment? ChildContent { get; set; }

    private async Task selectArea()
    {
        await SessionStorage.SetItemAsync("selectedArea", Lieu);

        if (Lieu.requires_SQF)
        {
            Navigation.NavigateTo("/SQF");
        }
        else
        {
            Navigation.NavigateTo("/Signature");
        }
    }
}
