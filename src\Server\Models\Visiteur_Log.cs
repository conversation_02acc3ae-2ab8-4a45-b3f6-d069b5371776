﻿namespace VisitLog3.Models
{
    [PetaPoco.PrimaryKey("id_visiteur_log")]
    [PetaPoco.TableName("visiteur_log")]
    public partial class Visiteur_Log
    {
        public int id_visiteur_log { get; set; }
        public string prenom { get; set; }
        public string nom { get; set; }
        public string compagnie { get; set; }
        public string telephone { get; set; }
        public string contact_pex_nom { get; set; }
        public int contact_pex_id { get; set; }
        public string langue { get; set; }
        public int? id_lieux { get; set; }
        public string? entry_point { get; set; }

        public DateTime? date_heure_arrivee { get; set; }
        public DateTime? date_heure_depart { get; set; }

        public bool deleted { get; set; }

        public string scan { get; set; }
        public string signature_png_base64 { get; set; }

        [PetaPoco.Ignore]
        public IEnumerable<Lieu> LieuxOptions { get; set; }

        [PetaPoco.Ignore]
        public string lieu_name
        {
            get => LieuxOptions?.FirstOrDefault(lieu => lieu.id_lieux == id_lieux)?.nom_fr ?? "Unknown";
        }

        public int? gx_error_code { get; set; }

        [PetaPoco.Ignore]
        public IEnumerable<Gx_soap_api_errors> GxSoapApiErrorsOptions { get; set; }

        [PetaPoco.Ignore]
        public string gx_error_message
        {
            get => GxSoapApiErrorsOptions?.FirstOrDefault(error => error.error_code == gx_error_code)?.error_message ?? "";
        }

        [PetaPoco.Ignore]
        public IEnumerable<lieu_carteAcces> cardNumbers { get; set; }

        [PetaPoco.Ignore]
        public int? card_number
        {
            get => cardNumbers?.FirstOrDefault(card => card.id_carte_acces == scan)?.numero_carte;
        }
    }
}
