﻿using PetaPoco;

namespace VisitLog3.Models
{
    public partial class lieu_carteAcces
    {
        public int id_lieu { get; set; }
        public string id_carte_acces { get; set; }
        public int numero_carte { get; set; }
        public int gx_user_id { get; set; }
        public string user_GX { get; set; }

        public int recurrent { get; set; }

        public DateTime? last_activation_time { get; set; }

    }

}
