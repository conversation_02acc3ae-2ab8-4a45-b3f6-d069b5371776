﻿@page "/Admin"
@using System.Security.Claims
@inject AuthenticationStateProvider AuthenticationStateProvider

<PageTitle>Admin</PageTitle>

<AuthorizeView>
    <Authorized>
        <AdminGrid></AdminGrid>
    </Authorized>
    <NotAuthorized>
        <MicrosoftExternalLoginRedirect />
    </NotAuthorized>
</AuthorizeView>

<script>
    window.downLoadFile = (fileName, url) => {
        const anchorElement = document.createElement('a');
        anchorElement.href = url;
        anchorElement.download = fileName ?? '';
        anchorElement.click();
        anchorElement.remove();
    }

</script>




