using Microsoft.AspNetCore.SignalR;
using VisitLog3.Models;

namespace VisitLog3.Hubs
{
    public class VisitorLogHub : Hub
    {
        public async Task addVisitor(Visiteur_Log visitorLog)
        {
            await Clients.All.SendAsync("ReceiveVisitor", visitorLog);
        }

        public async Task UpdateVisitorDeparture(Visiteur_Log updatedVisitorLog)
        {
            await Clients.All.SendAsync("UpdateVisitorDeparture", updatedVisitorLog);
        }
    }
}
