﻿console.log('✅ keepalive.js LOADED');      // <‑‑ add me

window.visitlogKeepAlive = {
    start: function () {
        console.log('▶️ keep‑alive TIMER STARTED');   // 2nd log
        setInterval(() => {
            fetch('/_blazor/keep-alive', { method: 'GET', cache: 'no-cache' })
                .then(() => console.log('[keep‑alive] ping', new Date().toLocaleTimeString()))
                .catch(err => console.warn('[keep‑alive] failed', err));
        }, 30_000);
    }
};
