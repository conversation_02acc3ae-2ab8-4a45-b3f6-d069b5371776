﻿console.log('✅ keepalive.js LOADED');

window.visitlogKeepAlive = {
    start: function () {
        console.log('▶️ keep‑alive TIMER STARTED');

        // Use the correct Blazor Server keep-alive mechanism
        setInterval(() => {
            // Send a ping to keep the circuit alive
            if (window.Blazor && window.Blazor._internal && window.Blazor._internal.sendPing) {
                window.Blazor._internal.sendPing();
                console.log('[blazor-ping] sent', new Date().toLocaleTimeString());
            } else {
                // Fallback to HTTP ping if Blazor internals are not available
                fetch('/_blazor/negotiate', {
                    method: 'POST',
                    cache: 'no-cache',
                    headers: {
                        'Content-Type': 'application/json'
                    }
                })
                .then(() => console.log('[http-ping] sent', new Date().toLocaleTimeString()))
                .catch(err => console.warn('[ping] failed', err));
            }
        }, 10_000); // Ping every 10 seconds to be well within the 15s keep-alive interval
    }
};
