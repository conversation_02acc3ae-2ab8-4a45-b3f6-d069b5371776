﻿using PetaPoco;

namespace VisitLog3.Models
{
    [TableName("EMPLOYE")]
    [PrimaryKey("ID_EMPLOYE")]
    public partial class Contact
    {
        public int ID_EMPLOYE { get; set; }
        public short? NO_EMPLOYE { get; set; }
        public string NOM { get; set; }
        public string PRENOM { get; set; }
        public int? ID_FONCTION { get; set; }
        public int? ID_EQUIPE { get; set; }
        public bool EMPLOYE_USINE { get; set; }
        public bool INACTIF { get; set; }
        public int? RELEASE { get; set; }
        public bool CHEF_EQUIPE { get; set; }
        public bool ADMIN { get; set; }
        public int? POIDS { get; set; }
        public DateTime? DATE_EXTINCTION { get; set; }
        public string EMAIL { get; set; }
        public bool DISPONIBLE_PLAINTE { get; set; }
        public string NO_POSTE { get; set; }
        public bool SERVICE_CLIENTELE { get; set; }
        public bool HAS_HORAIRE { get; set; }
        public bool SEE_HORAIRE_OTHER_EMP { get; set; }
        public bool RESP_PROJET { get; set; }
        public bool CONDUCTEUR_LIFT_APPROVED { get; set; }
        public byte[] RowVersion { get; set; }
    }
}
