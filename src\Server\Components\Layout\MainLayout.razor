﻿@inherits LayoutComponentBase
@inject IJSRuntime JS

<script src="/keepalive.js"></script>

<MudThemeProvider @rendermode="InteractiveServer" />
<MudPopoverProvider @rendermode="InteractiveServer" />
<MudDialogProvider @rendermode="InteractiveServer" />
<MudSnackbarProvider @rendermode="InteractiveServer" />

<div class="page">
    <main>

        <article class="content px-4">
                @Body
        </article>
    </main>
</div>


@code {
    protected override async Task OnAfterRenderAsync(bool firstRender)
    {
        if (firstRender)
        {
            await JS.InvokeVoidAsync("visitlogKeepAlive.start");
        }
    }
}