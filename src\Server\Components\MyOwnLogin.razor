﻿@using System.Security.Claims
@using Microsoft.AspNetCore.Authentication
@inject NavigationManager NavigationManager

<EditForm Model="@Model" OnSubmit="@Submit" FormName="MyLoginEditForm">
    Name: <InputText @bind-Value="Model!.Name" /><br />
    <button type="submit">Submit</button>
</EditForm>

@code {
    [SupplyParameterFromForm]
    public Credential? Model { get; set; }

    [CascadingParameter]
    private HttpContext HttpContext { get; set; } = default!;

    protected override void OnInitialized()
    {
        Model ??= new();
    }

    private void Submit()
    {
        if (Model is not null)
        {
            Console.WriteLine($"User Name: {Model.Name}");
            // Create security context
            var claims = new List<Claim>
                {
                    new Claim(ClaimTypes.Name, Model.Name!),
                };

            var identity = new ClaimsIdentity(claims, "TdtsCookie");
            var claimsPrincipal = new ClaimsPrincipal(identity);

            HttpContext.SignInAsync("TdtsCookie", claimsPrincipal);

            NavigationManager.NavigateTo("/");
        }
    }

    public class Credential
    {
        public string? Name { get; set; }

    }
}
