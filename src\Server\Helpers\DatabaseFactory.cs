﻿namespace VisitLog3.Helpers
{
    public class DatabaseFactory
    {
        private readonly IConfiguration _configuration;

        public DatabaseFactory(IConfiguration configuration)
        {
            _configuration = configuration;
        }

        public PetaPoco.Database GetDatabase(string databaseName)
        {
            var connectionString = _configuration.GetConnectionString(databaseName);
            if (string.IsNullOrEmpty(connectionString))
            {
                throw new ArgumentException($"Connection string for '{databaseName}' is not configured.");
            }

            return new PetaPoco.Database(connectionString, "System.Data.SqlClient");
        }
    }
}
