﻿@rendermode InteractiveServer
@inject IConfiguration Configuration
@inject NavigationManager NavigationManager
@inject IJSRuntime JSRuntime
@inject DatabaseFactory DatabaseFactory
@inject HttpClient Http
@inject NavigationManager Navigation
@inject IHttpContextAccessor HttpContextAccessor
@using Microsoft.AspNetCore.SignalR.Client
@implements IAsyncDisposable


<style>
    body {
    background: none !important;
    background-color: #fff !important;
    }
</style>

<div class="d-flex align-items-center py-3 top-16">

    <MyOwnLogout></MyOwnLogout>

    <div class="d-flex ms-auto align-items-center">

        <MudSwitch @bind-Value=@readOnly ThumbIcon="@(readOnly==true ? Icons.Material.Filled.Visibility : Icons.Material.Filled.Edit)" ThumbIconColor="Color.Primary" Size="Size.Large"></MudSwitch>

        <button class="secondary-btn d-flex align-items-center justify-content-center py-2 px-4 ms-2" aria-label="Export to Excel" @onclick="ExportDataAsync">
            <span style="margin-right: 8px;">Export Excel</span>
            <img src="/images/file-excel-regular.png" alt="Excel Icon" style="width: 16px; height: 20px;">
        </button>

    </div>
</div>

<MudDataGrid T=Visiteur_Log @ref=@refMudDataGrid ShowColumnOptions="false" SortMode="SortMode.None" ColumnResizeMode="MudBlazor.ResizeMode.None" CommittedItemChanges="@CommittedItemChanges" Class="adminGrid" Striped="true" Items="@visitors" Bordered="true" ReadOnly=@readOnly EditMode="DataGridEditMode.Cell" Filterable="true" Dense="true" EditTrigger="DataGridEditTrigger.OnRowClick" FilterMode="@DataGridFilterMode.ColumnFilterMenu" RowStyleFunc="RowStyle">
    <Columns>
        <PropertyColumn Property="x => x.nom" HeaderClass="admin-grid-header" Title="Nom" />
        <PropertyColumn Property="x => x.prenom" HeaderClass="admin-grid-header" Title="Prénom" />
        <PropertyColumn Property="x => x.compagnie" HeaderClass="admin-grid-header" Title="Compagnie" />
        <PropertyColumn Property="x => x.telephone" HeaderClass="admin-grid-header" Title="Téléphone" />
        <PropertyColumn Property="x => x.contact_pex_nom" HeaderClass="admin-grid-header" Title="Contact" Required="false" />
        <PropertyColumn CellStyle="min-width: 150px;" Property="x => x.lieu_name" HeaderClass="admin-grid-header" Title="Lieux d'accès" ShowFilterIcon="true" Editable="false">
            <FilterTemplate>
                <MudStack Spacing="1">
                    @foreach (var option in lieuxOptions)
                    {
                        <MudCheckBox T="bool" Label="@option.nom_fr" Size="Size.Small" Value="@(_selectedItems.Contains(option))"
                        ValueChanged="@((value) => SelectedChanged(value, option))" />
                    }
                </MudStack>
                <MudStack Row="true" Justify="Justify.FlexEnd">
                    <MudButton OnClick="@(() => ClearFilterAsync(context))">Clear</MudButton>
                    <MudButton Color="@Color.Primary" OnClick="@(() => ApplyFilterAsync(context))">Filter</MudButton>
                </MudStack>
            </FilterTemplate>
            <EditTemplate>
                <MudDataGridSelectLieu Context="@context">
                    @foreach (var lieuxOption in lieuxOptions)
                    {
                        <MudSelectItem Value="@lieuxOption.id_lieux">@lieuxOption.nom_fr</MudSelectItem>
                    }
                </MudDataGridSelectLieu>
            </EditTemplate>
        </PropertyColumn>
        <PropertyColumn Property="x => x.card_number" HeaderClass="admin-grid-header" Title="N°" Editable="false" />
        <PropertyColumn Property="x => x.date_heure_arrivee" HeaderClass="admin-grid-header" Title="Arrivée">
            <EditTemplate>
                <AdminDataGridArrivalTime Context="@context" />
            </EditTemplate>
        </PropertyColumn>
        <PropertyColumn Property="x => x.date_heure_depart" HeaderClass="admin-grid-header" Title="Départ">
            <EditTemplate>
                <AdminDataGridDepartureTime Context="@context" />
            </EditTemplate>
        </PropertyColumn>
        <PropertyColumn Property="x => x.entry_point" HeaderClass="admin-grid-header" Title="Point d'entrée" />
        <TemplateColumn>
            <HeaderTemplate>
                <MudText Typo="Typo.body2"><b>Erreur GX</b></MudText>
            </HeaderTemplate>
            <CellTemplate>
                <MudText Typo="Typo.body2">@context.Item.gx_error_message</MudText>
            </CellTemplate>
            <EditTemplate>
                <MudStack Row="true" AlignItems="AlignItems.Center">
                    @if (context.Item.gx_error_code != null)
                    {
                        <MudTooltip Text="Retry GX API call">
                            <MudIconButton Size="Size.Small"
                            OnClick="@(() => reprocessGXError(context.Item))"
                            Color="@Color.Default"
                            Icon="@Icons.Material.Filled.Refresh" />
                        </MudTooltip>
                    }
                    <MudText Typo="Typo.body2">@context.Item.gx_error_message</MudText>
                </MudStack>
            </EditTemplate>
        </TemplateColumn>
        <TemplateColumn>
            <EditTemplate>
                <MudTooltip Text="Delete visit entry">
                    <MudIconButton Size="Size.Small" OnClick="@(() => PrcdBtnClick(@context.Item))" Color="@Color.Default" Icon="@Icons.Material.Filled.Delete" />
                </MudTooltip>
            </EditTemplate>
        </TemplateColumn>
    </Columns>
    <PagerContent>
        <MudDataGridPager PageSizeOptions=[20] PageSizeSelector=false T=Visiteur_Log />
    </PagerContent>
</MudDataGrid>

<MudPopover Open="@_openVisitorLogDeletionPopover" AnchorOrigin="Origin.CenterCenter" TransformOrigin="Origin.CenterCenter">
    <MudStack Class="pa-8">

        <MudText Typo="Typo.h6" Align="Align.Center">Êtes-vous sûr de vouloir supprimer l'enregistrement suivant?</MudText>
        <MudStack Row="true" Justify="Justify.Center">
            <MudText Align="Align.Center"><b>Nom :</b> @SelectedVisitorLogForDeletion.nom, @SelectedVisitorLogForDeletion.prenom</MudText>
            <MudText Align="Align.Center"><b>Arrivée :</b> @SelectedVisitorLogForDeletion.date_heure_arrivee</MudText>
        </MudStack>
        <MudStack Row="true" Justify="Justify.SpaceBetween">
            <MudButton OnClick="cancelVisitLogDeletion">Non</MudButton>
            <MudButton Color="@Color.Error" OnClick="deleteVisitorLog">Oui</MudButton>
        </MudStack>
    </MudStack>
</MudPopover>

<MudOverlay @bind-Visible="_openVisitorLogDeletionVisible" DarkBackground AutoClose="true" OnClosed="closeVisitorLogDeletionPopover" />

@code {

    private List<Visiteur_Log> visitors = new List<Visiteur_Log>();

    private List<Gx_soap_api_errors> allErrors = new();
    private List<lieu_carteAcces> accessCards = new();

    private IEnumerable<Lieu> lieuxOptions = new List<Lieu>();
    private MudDataGrid<Visiteur_Log> refMudDataGrid;
    HashSet<Lieu> _selectedItems = new();
    HashSet<Visiteur_Log> _filterItems = new();
    FilterDefinition<Visiteur_Log> _filterDefinition;
    private Visiteur_Log? SelectedVisitorLogForDeletion;
    private bool readOnly = true;

    private bool _openVisitorLogDeletionPopover = false;
    private bool _openVisitorLogDeletionVisible = false;
    private FilterDefinition<Visiteur_Log>? _receptionFilter;

    private HubConnection _hubConnection;


    protected override async Task OnAfterRenderAsync(bool firstRender)
    {
        if (firstRender)
        {
            var hubUrl = NavigationManager.ToAbsoluteUri("/VisitorLogHub").ToString();

            _hubConnection = new HubConnectionBuilder()
                .WithUrl(hubUrl)
                .WithAutomaticReconnect(new[] { TimeSpan.Zero, TimeSpan.FromSeconds(2), TimeSpan.FromSeconds(10), TimeSpan.FromSeconds(30) })
                .Build();

            // Handle connection events
            _hubConnection.Closed += async (error) =>
            {
                Console.WriteLine($"SignalR connection closed: {error?.Message}");
                await Task.Delay(new Random().Next(0, 5) * 1000);
                try
                {
                    await _hubConnection.StartAsync();
                }
                catch (Exception ex)
                {
                    Console.WriteLine($"Error restarting SignalR connection: {ex.Message}");
                }
            };

            _hubConnection.Reconnecting += (error) =>
            {
                Console.WriteLine($"SignalR reconnecting: {error?.Message}");
                return Task.CompletedTask;
            };

            _hubConnection.Reconnected += (connectionId) =>
            {
                Console.WriteLine($"SignalR reconnected: {connectionId}");
                return Task.CompletedTask;
            };


            _hubConnection.On<Visiteur_Log>("ReceiveVisitor", (newVisitor) =>
            {
                // Populate fields just like you did for existing visitors
                newVisitor.LieuxOptions = lieuxOptions;
                newVisitor.GxSoapApiErrorsOptions = allErrors;
                newVisitor.cardNumbers = accessCards;

                // If "card_number" needs to come from accessCards based on newVisitor.scan, do:
                // newVisitor.card_number = accessCards.FirstOrDefault(a => a.id_carte_acces == newVisitor.scan)?.someField;

                visitors.Insert(0, newVisitor);
                InvokeAsync(StateHasChanged);
            });




            _hubConnection.On<Visiteur_Log>("UpdateVisitorDeparture", (updatedVisitor) =>
                {
                    var existingVisitor = visitors.FirstOrDefault(v => v.id_visiteur_log == updatedVisitor.id_visiteur_log);
                    if (existingVisitor != null)
                    {
                        existingVisitor.date_heure_depart = updatedVisitor.date_heure_depart;
                        InvokeAsync(StateHasChanged);
                    }
                });

            try
            {
                await _hubConnection.StartAsync();
                Console.WriteLine("SignalR connection started successfully");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error starting SignalR connection: {ex.Message}");
            }
        }

        var locationCookie = HttpContextAccessor.HttpContext?.Request.Cookies["location"];

        if (locationCookie == "Reception")
        {
            await ApplyReceptionFilterAsync();
        }

    }

    private async Task ApplyReceptionFilterAsync()
    {
        // wait until the grid really has its columns
        if (refMudDataGrid?.RenderedColumns is null ||
            refMudDataGrid.RenderedColumns.Count == 0)
            return;

        // the column that holds the location text
        var locationColumn = refMudDataGrid.RenderedColumns
                                           .First(c => c.PropertyName ==
                                                       nameof(Visiteur_Log.entry_point));

        _receptionFilter = new FilterDefinition<Visiteur_Log>
            {
                Column = locationColumn,
                Operator = FilterOperator.String.Equal,
                Value = "Reception"
            };

        await refMudDataGrid.AddFilterAsync(_receptionFilter);  // <‑‑ the magic line
                                                                // no StateHasChanged() needed; the grid refreshes itself
    }

    private void cancelVisitLogDeletion(){

        SelectedVisitorLogForDeletion = null;
        _openVisitorLogDeletionVisible = false;
        _openVisitorLogDeletionPopover = false;

    }

    private void deleteVisitorLog()
    {
        if (SelectedVisitorLogForDeletion != null)
        {
            SelectedVisitorLogForDeletion.deleted = true;

            var db = DatabaseFactory.GetDatabase("dbInformatique");

            db.Update("VISITEUR_LOG", "id_visiteur_log", SelectedVisitorLogForDeletion);

            visitors = visitors.Where(v => v.id_visiteur_log != SelectedVisitorLogForDeletion.id_visiteur_log).ToList();

            _openVisitorLogDeletionVisible = false;
            _openVisitorLogDeletionPopover = false;

        }
    }


    private void closeVisitorLogDeletionPopover()
    {
        _openVisitorLogDeletionPopover = false;
    }

    private void reprocessGXError(Visiteur_Log x)
    {
        string apiUrl = $"api/scan/reprocessGXError?visitorID={x.id_visiteur_log}";
        Navigation.NavigateTo(apiUrl, forceLoad: true);
    }


    private void PrcdBtnClick(Visiteur_Log x)
    {
        SelectedVisitorLogForDeletion = x;
        _openVisitorLogDeletionPopover = true;
        _openVisitorLogDeletionVisible = true;

    }

    private async Task ClearFilterAsync(FilterContext<Visiteur_Log> context)
    {
        _selectedItems = new();
        _filterItems = visitors.ToHashSet();
        await context.Actions.ClearFilterAsync(_filterDefinition);

    }

    private async Task ApplyFilterAsync(FilterContext<Visiteur_Log> context)
    {
        if (_selectedItems.Any())
        {
            _filterItems = visitors
                .Where(visitor => visitor.id_lieux.HasValue && _selectedItems.Any(lieu => lieu.id_lieux == visitor.id_lieux.Value))
                .ToHashSet();
        }
        else
        {
            _filterItems = visitors.ToHashSet();
        }

        await context.Actions.ApplyFilterAsync(_filterDefinition);
    }

    private void SelectedChanged(bool value, Lieu item)
    {
        if (value)
            _selectedItems.Add(item);
        else
            _selectedItems.Remove(item);

    }

    protected override async Task OnInitializedAsync()
    {

        var connectionString = Configuration.GetConnectionString("dbInformatique");
        using var db = new PetaPoco.Database(connectionString, "System.Data.SqlClient");

        visitors = await db.FetchAsync<Visiteur_Log>("SELECT * FROM Visiteur_Log WHERE deleted=0");
        lieuxOptions = await db.FetchAsync<Lieu>("SELECT id_lieux, nom_fr FROM Lieux");
        allErrors = await db.FetchAsync<Gx_soap_api_errors>("SELECT * FROM GX_SOAP_API_ERRORS");
        accessCards = await db.FetchAsync<lieu_carteAcces>("SELECT * FROM lieu_carteAcces");

        foreach (var visitor in visitors)
        {
            visitor.LieuxOptions = lieuxOptions;
            visitor.GxSoapApiErrorsOptions = allErrors;
            visitor.cardNumbers = accessCards;
        }

        visitors = visitors
            .OrderBy(v => v.date_heure_depart.HasValue)
            .ThenByDescending(v => v.date_heure_arrivee)
            .ToList();

        _filterDefinition = new FilterDefinition<Visiteur_Log>
            {
                FilterFunction = x => _filterItems.Contains(x)
            };
    }

    private async Task NavigateBack()
    {

        await JSRuntime.InvokeVoidAsync("redirectTo", "/Accueil");
    }

    private async Task NavigateToAccueil()
    {
        await JSRuntime.InvokeVoidAsync("redirectTo", "/Accueil");
    }

    private async Task CommittedItemChanges(Visiteur_Log item)
    {
        var connectionString = Configuration.GetConnectionString("dbInformatique");
        using var db = new PetaPoco.Database(connectionString, "System.Data.SqlClient");

        var username = HttpContextAccessor.HttpContext?.User?.Identity?.Name ?? "unknown";

        var oldItem = await db.SingleOrDefaultAsync<Visiteur_Log>("SELECT * FROM VISITEUR_LOG WHERE id_visiteur_log=@0", item.id_visiteur_log);

        await db.UpdateAsync("VISITEUR_LOG", "id_visiteur_log", item);

        if (oldItem != null)
        {
            await db.InsertAsync("VISITEUR_LOG_AUDIT", new
            {
                ID_VISITEUR_LOG = oldItem.id_visiteur_log,
                ACTION_TYPE = "UPDATE",
                MODIFIED_BY = username,
                MODIFIED_AT = DateTime.Now,
                PRENOM = oldItem.prenom,
                NOM = oldItem.nom,
                COMPAGNIE = oldItem.compagnie,
                TELEPHONE = oldItem.telephone,
                CONTACT_PEX_NOM = oldItem.contact_pex_nom,
                CONTACT_PEX_ID = oldItem.contact_pex_id,
                LANGUE = oldItem.langue,
                ID_LIEUX = oldItem.id_lieux,
                ENTRY_POINT = oldItem.entry_point,
                DATE_HEURE_ARRIVEE = oldItem.date_heure_arrivee,
                DATE_HEURE_DEPART = oldItem.date_heure_depart,
                DELETED = oldItem.deleted,
                SCAN = oldItem.scan,
                SIGNATURE_PNG_BASE64 = oldItem.signature_png_base64,
                GX_ERROR_CODE = oldItem.gx_error_code
            });
        }

        StateHasChanged();
    }



    private string RowStyle(Visiteur_Log log, int index)
    {
        if (log.date_heure_arrivee.HasValue)
        {
            if (!log.date_heure_depart.HasValue)
            {
                var gap = DateTime.Now - log.date_heure_arrivee.Value;
                if (gap.TotalHours > 12)
                {
                    return "background-color: #ffcccc; color: black;";
                }
            }
        }
        return string.Empty;
    }

    private async Task ExportDataAsync()
    {

        string strFileName = HelpFileManagement.CreateFileName("MyFileName");

        strFileName = HelpFileManagement.CreateFullFileName(strFileName, ExcelCSVExport.Enums.ExportFormat.Excel);

        string strFullFilePath = Path.Combine(serApp.WebRootExportFilesPath, strFileName);

        await HelpDataGridExporter<Visiteur_Log>.ExportExcelAsync(refMudDataGrid, strFullFilePath);

        string strFileUrl = $"/exportfiles/{strFileName}";

        await JSRuntime.InvokeVoidAsync("downLoadFile", strFileName, strFileUrl);

    }

    public async ValueTask DisposeAsync()
    {
        if (_hubConnection is not null)
        {
            await _hubConnection.DisposeAsync();
        }
    }

}
