﻿namespace VisitLog3.Models
{
    using PetaPoco;

    [<PERSON><PERSON><PERSON>("APPLICATION_PARAMETRE")]
    [PrimaryKey("ID")]
    public class Application_Parametre
    {
        [Column("ID")]
        public int Id { get; set; }

        [Column("APPLICATION")]
        public string Application { get; set; }

        [Column("SECTION")]
        public string Section { get; set; }

        [Column("CLE")]
        public string Cle { get; set; }

        [Column("DESC_FRA")]
        public string? DescFra { get; set; }

        [Column("DESC_ANG")]
        public string? DescAng { get; set; }

        [Column("TYPE")]
        public string Type { get; set; }

        [Column("VALEUR")]
        public string? Valeur { get; set; }

        [Column("UNITE")]
        public string? Unite { get; set; }

        [Column("ORDRE_AFFICHAGE")]
        public int OrdreAffichage { get; set; }
    }

}
