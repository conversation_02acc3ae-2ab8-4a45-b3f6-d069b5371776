﻿<Project Sdk="Microsoft.NET.Sdk.Web">

  <PropertyGroup>
    <TargetFramework>net9.0</TargetFramework>
    <Nullable>enable</Nullable>
    <ImplicitUsings>enable</ImplicitUsings>
    <UserSecretsId>77162421-ccda-4d88-8f38-b2d3856e62d0</UserSecretsId>
  </PropertyGroup>

  <ItemGroup>
    <None Include="wwwroot\images\arrow-left-solid.png" />
    <None Include="wwwroot\images\arrow-right-solid.png" />
    <None Include="wwwroot\images\cookie-bite-solid.png" />
    <None Include="wwwroot\images\eraser-solid.png" />
    <None Include="wwwroot\images\file-excel-regular.png" />
    <None Include="wwwroot\images\gear-solid.png" />
    <None Include="wwwroot\images\hd-blue-laser-beam.png" />
    <None Include="wwwroot\images\house-white.png" />
    <None Include="wwwroot\images\pex_background.png" />
  </ItemGroup>

  <ItemGroup>
    <PackageReference Include="Azure.Identity" Version="1.11.4" />
    <PackageReference Include="Blazored.SessionStorage" Version="2.4.0" />
    <PackageReference Include="DocumentFormat.OpenXml" Version="3.2.0" />
    <PackageReference Include="Microsoft.AspNetCore.Antiforgery" Version="2.3.0" />
    <PackageReference Include="Microsoft.AspNetCore.Authentication" Version="2.3.0" />
    <PackageReference Include="Microsoft.AspNetCore.Authentication.Google" Version="8.0.4" />
    <PackageReference Include="Microsoft.AspNetCore.Authentication.MicrosoftAccount" Version="8.0.4" />
    <PackageReference Include="Microsoft.AspNetCore.SignalR.Client" Version="9.0.2" />
    <PackageReference Include="Microsoft.Extensions.Hosting.WindowsServices" Version="9.0.1" />
    <PackageReference Include="Microsoft.Graph" Version="5.69.0" />
    <PackageReference Include="Microsoft.Identity.Web" Version="3.7.0" />
    <PackageReference Include="Microsoft.Identity.Web.UI" Version="3.7.0" />
    <PackageReference Include="Mobsites.Blazor.SignaturePad" Version="1.1.7" />
    <PackageReference Include="MudBlazor" Version="8.2.0" />
    <PackageReference Include="PetaPoco.Compiled" Version="6.0.683" />
    <PackageReference Include="ReactorBlazorQRCodeScanner" Version="1.0.10" />
    <PackageReference Include="System.Data.SqlClient" Version="4.9.0" />
    <PackageReference Include="System.ServiceModel.Duplex" Version="6.0.*" />
    <PackageReference Include="System.ServiceModel.Federation" Version="6.0.*" />
    <PackageReference Include="System.ServiceModel.Http" Version="6.0.*" />
    <PackageReference Include="System.ServiceModel.NetTcp" Version="6.0.*" />
    <PackageReference Include="System.ServiceModel.Security" Version="6.0.*" />
  </ItemGroup>

</Project>
