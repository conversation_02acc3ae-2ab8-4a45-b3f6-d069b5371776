﻿@page "/SQF"
@rendermode InteractiveServer
@inject IConfiguration Configuration
@inject NavigationManager Navigation
@inject Blazored.SessionStorage.ISessionStorageService SessionStorage

<div class="position-fixed top-0 end-0 m-3 text-muted small" style="opacity: 0.5;">
    @GetEntRevision()
</div>

<div class="container top-16">
    <div class="col-7 custom-offset">
        <div class="d-flex justify-content-between align-items-center py-3">

            <button class="secondary-btn d-flex align-items-center justify-content-center py-2 px-4" aria-label="Go Back" @onclick="NavigateBack">
                <img src="/images/arrow-left-solid.png" alt="House Icon" style="width: 20px; height: 20px;">
            </button>

            <h4 id="mainHeader" class="primary-header text-center my-0" @ondblclick="ToggleCheckBoxes">
                @GetLocalizedText("SQF - Aliments de Qualité Sécuritaire", "SQF - Safe Quality Food")
            </h4>

            <button class="secondary-btn d-flex align-items-center justify-content-center py-2 px-4" aria-label="Go to Home" @onclick="NavigateToAccueil">
                <img src="/images/house-white.png" alt="House Icon" style="width: 20px; height: 20px;">
            </button>
        </div>

        <MudForm @ref="form" class="mt-5" Spacing="1" @bind-IsValid="success" @bind-Errors="errors">
            @foreach (var sqf in ListeSQF.OrderBy(sqf => sqf.ordre))
            {
                var text = lng == "en" ? sqf.texte_ang : sqf.texte_fra;
                var modifiedText = text.Contains('*')
                ? $"<b>{text.Replace("*", "")}</b>"
                : text;
                var isLastTwo = ListeSQF.OrderBy(sqf => sqf.ordre).ToList().IndexOf(sqf) >= ListeSQF.Count - 2;

                <MudCheckBox @bind-Value="sqf.isChecked"
                             T="bool"
                             Required="true"
                             Class="@(isLastTwo ? "sqf-checkbox last-two-spacing" : "sqf-checkbox")">
                    @((MarkupString)modifiedText)
                </MudCheckBox>
            }
            <button type="button" @onclick="Next" style="margin-top: 27px;" class="secondary-btn float-end w-100 py-2">
                <img src="/images/arrow-right-solid.png" alt="House Icon" style="width: 20px; height: 20px;">
            </button>
        </MudForm>
    </div>
</div>

@code {
    private MudForm? form;
    private List<Visiteur_sqf> ListeSQF { get; set; } = new();
    private string lng = "en";
    private bool success;
    private string[] errors = { };
    private bool isInitialized = false;

    protected override async Task OnInitializedAsync()
    {
        // Fetch data from the database
        var db = new Database<SqlServerDatabaseProvider>(Configuration.GetConnectionString("dbInformatique"));
        ListeSQF = db.Fetch<Visiteur_sqf>();

    }

    protected override async Task OnAfterRenderAsync(bool firstRender)
    {
        if (firstRender)
        {
            // Get language preference from session storage
            lng = await SessionStorage.GetItemAsync<string>("lng") ?? "en";

            isInitialized = true;
            StateHasChanged();
        }
    }

    private string GetLocalizedText(string textFr, string textEn)
    {
        return lng == "en" ? textEn : textFr;
    }

    private async Task NavigateBack()
    {
        var scanCode = await SessionStorage.GetItemAsync<string>("scanCode");

        if (!string.IsNullOrWhiteSpace(scanCode))
        {
            Navigation.NavigateTo("/Identification");
        }
        else
        {
            Navigation.NavigateTo("/Lieux");
        }
    }


    private async Task NavigateToAccueil()
    {
        Navigation.NavigateTo("/");
    }

    private void ToggleCheckBoxes()
    {
        bool allChecked = ListeSQF.All(sqf => sqf.isChecked);
        bool newState = !allChecked;

        foreach (var sqf in ListeSQF)
        {
            sqf.isChecked = newState;
        }
    }

    private async Task Next()
    {
        await form.Validate();

        if (success)
        {
            Navigation.NavigateTo("/Signature");
        }
    }
    private string GetEntRevision()
    {
        return lng == "fr" ? "ENT-004 Révision 6" : "ENT-004 Révision 4";
    }

}
