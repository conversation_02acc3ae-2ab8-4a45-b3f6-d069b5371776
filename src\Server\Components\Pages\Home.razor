﻿@page "/"
@rendermode InteractiveServer
@inject Microsoft.AspNetCore.Components.Server.ProtectedBrowserStorage.ProtectedSessionStorage ProtectedStore
@using Microsoft.AspNetCore.Authentication
@using Microsoft.AspNetCore.Components.Authorization
@using System.Security.Claims
@inject IConfiguration Configuration
@inject Blazored.SessionStorage.ISessionStorageService SessionStorage
@inject NavigationManager Navigation
@inject IHttpContextAccessor HttpContextAccessor
@inject IHttpClientFactory ClientFactory

<PageTitle>Home</PageTitle>

<div class="position-fixed top-0 end-0 m-3 text-muted small" style="opacity: 0.5;">
    RQA-009 Révision 5
</div>

<div class="position-fixed top-0 end-0  m-3">

<MudPopover Open="_placementConfigurationPopoverOpen"
            AnchorOrigin="Origin.BottomRight"
            TransformOrigin="Origin.TopLeft">
        <MudPaper Class="pa-4 ma-2" Elevation="0">
            <MudStack>
                <MudText Typo="Typo.h6" Class="fw-bold">Choi<PERSON><PERSON><PERSON> le poste</MudText>
                @foreach (var poste in postes)
                {
                    <MudButton OnClick="@(() => CreateCookieButtonHandler(poste))" Variant="Variant.Text">
                        @poste
                    </MudButton>
                }
            </MudStack>
        </MudPaper>
</MudPopover>

</div>
@*<MudButton OnClick="testAPI">test api</MudButton>*@


<div class="position-relative" style="height: 90vh; width: 100%">
    <div class="container position-absolute top-50 start-50 translate-middle">
        <div class="col-7 custom-offset">
            <div class="d-flex flex-column align-items-center">
                <button class="btn-lg p-5 fw-bold fs-1 primary-btn w-75" style="margin-bottom: 50px;" @onclick="NavigateToIdentificationFR">Bienvenue</button>
                <button class="btn-lg p-5 fw-bold fs-1 primary-btn w-75" style="margin-bottom: 50px;" @onclick="NavigateToIdentificationEN">Welcome</button>
                <div class="d-flex justify-content-center">
                    <button class="secondary-btn btn-lg fw-bold fs-3" style="margin-bottom: 50px;" @onclick="() => NavigateToScan(false)">Départ / Leaving</button>
                </div>
                <div class="d-flex justify-content-center">
                    <button class="btn btn-link fs-5 text-decoration-underline text-muted" @onclick="() => NavigateToScan(true)" style="border: none; background: none; padding: 0;">
                        Visiteur récurrent / Recurring Visitor
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>

@code {

    private bool _placementConfigurationPopoverOpen = false;
    private List<string> postes = new();

    protected override async Task OnInitializedAsync()
    {
        var connectionString = Configuration.GetConnectionString("dbInformatique");
        using var db = new PetaPoco.Database(connectionString, "System.Data.SqlClient");
        postes = (await db.FetchAsync<string>("SELECT DISTINCT Config FROM Config_Lieu")).ToList();

        // Check if the location cookie exists
        var cookies = HttpContextAccessor.HttpContext.Request.Cookies;
        if (!cookies.ContainsKey("location"))
        {
            // Open the popup if the location cookie is missing
            _placementConfigurationPopoverOpen = true;
        }
    }

    private async void testAPI()
    {
        var handler = new HttpClientHandler
            {
                ServerCertificateCustomValidationCallback = (message, cert, chain, errors) => true
            };

        var client = new HttpClient(handler);

        var request = new HttpRequestMessage(HttpMethod.Get, "https://localhost:5001/api/test");

        try
        {
            var response = await client.SendAsync(request);

            if (response.IsSuccessStatusCode)
            {
                Console.WriteLine($"API call succeeded: {await response.Content.ReadAsStringAsync()}");
            }
            else
            {
                Console.WriteLine($"API call failed: {response.StatusCode}");
            }
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Exception during API call: {ex.Message}");
        }
    }

    private void TogglePlacementConfigurationPopover()
    {
        _placementConfigurationPopoverOpen = !_placementConfigurationPopoverOpen;
    }

    private void ClosePlacementConfigurationPopover()
    {
        _placementConfigurationPopoverOpen = false;
    }

    private void CreateCookieButtonHandler()
    {
        Navigation.NavigateTo("api/clc", true);
    }

    private void ClearLocationCookie()
    {
        // Remove the cookie
        Navigation.NavigateTo("api/clearLocationCookie", true);
    }

    private void CreateCookieButtonHandler(string buttonType)
    {
        string apiUrl = $"api/clc?buttonType={buttonType}";
        Navigation.NavigateTo(apiUrl, forceLoad: true);
    }

    protected override async Task OnAfterRenderAsync(bool firstRender)
    {
        if (firstRender)
        {
            await SessionStorage.ClearAsync();
            StateHasChanged();
        }
    }
    private async Task NavigateToIdentificationFR()
    {
        await ProtectedStore.SetAsync("test", "encrypted");
        await SessionStorage.SetItemAsync("lng", "fr");
        Navigation.NavigateTo("/start/Entry", forceLoad: true);
    }

    private async Task NavigateToIdentificationEN()
    {

        await SessionStorage.SetItemAsync("lng", "en");
        Navigation.NavigateTo("/start/Entry", forceLoad: true);
    }

    private void NavigateToScan(bool isRecurring = false)
    {
        var mode = isRecurring ? "Recurring" : "Departure";
        Navigation.NavigateTo($"/start/{mode}", forceLoad: true);
    }


    private void NavigateToAdmin()
    {
        Navigation.NavigateTo("/Admin");
    }

}