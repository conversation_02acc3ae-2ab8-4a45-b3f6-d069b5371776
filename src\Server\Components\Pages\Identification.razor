﻿@page "/Identification"
@rendermode InteractiveServer
@inject NavigationManager NavigationManager
@inject Blazored.SessionStorage.ISessionStorageService SessionStorage
@inject IConfiguration Configuration
@using System.Text.RegularExpressions
@using System.ComponentModel.DataAnnotations
@inject IJSRuntime JS


<div class="container top-16">
    <div class="col-7 custom-offset">
        <div class="d-flex justify-content-between align-items-center py-3">
            <!-- Back Button -->
            <button class="secondary-btn d-flex align-items-center justify-content-center py-2 px-4" aria-label="Go Back" @onclick="NavigateToAccueil">
                <img src="/images/arrow-left-solid.png" alt="House Icon" style="width: 20px; height: 20px;">
            </button>

            <!-- Home Button -->
            <button class="secondary-btn d-flex align-items-center justify-content-center py-2 px-4" aria-label="Go to Home" @onclick="NavigateToAccueil">
                <img src="/images/house-white.png" alt="House Icon" style="width: 20px; height: 20px;">
            </button>
        </div>
        <MudForm style="margin-top: 20px;" @ref="form" @bind-IsValid="success" @bind-Errors="errors" Spacing="5">

            <!-- Prenom Field -->
            <MudTextField T="string"
                          Label="@(lng == "en" ? "First Name" : "Prénom")"
                          @bind-Value="prenom"
                          Required="true" />

            <!-- Nom Field -->
            <MudTextField T="string"
                          Label="@(lng == "en" ? "Last Name" : "Nom")"
                          @bind-Value="nom"
                          Required="true" />

            <!-- telephone Field -->
            <MudTextField 
                Id="phoneInput"
                T="string"
                          InputType="InputType.Telephone"
                          Label='@(lng == "en" ? "Telephone #" : "# Téléphone")'
                          @bind-Value="telephone"
                          Required="true" />

            <!-- Compagnie Field -->
            <MudTextField T="string"
                        Label="@(lng == "en" ? "Company" : "Compagnie")"
                        @bind-Value="compagnie"
                          Required="true" />

            <!-- Contact Autocomplete -->

            <MudAutocomplete T="Contact" MaxItems="9999999"
            @bind-Value="_selectedContact"
            @bind-Text="_customContactInput"
            SearchFunc="SearchContacts"
            ToStringFunc="@(contact => contact != null ? $"{contact.PRENOM} {contact.NOM}" : string.Empty)"
            Variant="Variant.Text"
            Label="@(lng == "en" ? "Contact" : "Contact")"
            PopoverClass="custom-popover"
            CoerceText="false"
            CoerceValue="true"
            style="padding-left: 3px; padding-right: 3px;"
                             Required="true" />


            <button type="button" @onclick="Save" style="margin-top: 27px;" class="secondary-btn float-end w-100 py-2">
                <img src="/images/arrow-right-solid.png" alt="House Icon" style="width: 20px; height: 20px;">
            </button>

        </MudForm>

    </div>
</div>

@code {

    [Parameter]
    [SupplyParameterFromQuery(Name = "mode")]
    public string? Mode { get; set; }

    private MudForm form;
    private bool success;
    private string[] errors = { };
    private bool isInitialized = false;

    // Bound values
    private string telephone;
    private string prenom;
    private string nom;
    private string compagnie;
    private string _customContactInput;

    private ISession session;

    private Contact? _selectedContact;
    private List<Contact> _contacts = new();
    private string lng;

    protected override async Task OnAfterRenderAsync(bool firstRender)
    {
        if (firstRender)
        {
            telephone = await SessionStorage.GetItemAsync<string>("telephone") ?? string.Empty;
            prenom = await SessionStorage.GetItemAsync<string>("prenom") ?? string.Empty;
            nom = await SessionStorage.GetItemAsync<string>("nom") ?? string.Empty;
            compagnie = await SessionStorage.GetItemAsync<string>("compagnie") ?? string.Empty;

            _selectedContact = await SessionStorage.GetItemAsync<Contact>("selectedContact");
            if (_selectedContact != null && string.IsNullOrWhiteSpace(_selectedContact.NOM))
            {
                _selectedContact = null;
            }
            if (_selectedContact != null){
                _customContactInput = $"{_selectedContact.PRENOM} {_selectedContact.NOM}";
            }
            
            lng = await SessionStorage.GetItemAsync<string>("lng") ?? "en";

            await JS.InvokeVoidAsync("initPhoneMask", "phoneInput");

            LoadContacts();

            isInitialized = true;
            StateHasChanged();
        }
    }

    private void LoadContacts()
    {
        var connectionString = Configuration.GetConnectionString("dbPEX");
        using var db = new PetaPoco.Database(connectionString, "System.Data.SqlClient");

        _contacts = db.Fetch<Contact>("WHERE CAN_ACCEPT_VISITORS = 1 AND INACTIF = 0 ORDER BY PRENOM").ToList();
    }

    private async Task<IEnumerable<Contact>> SearchContacts(string value, CancellationToken cancellationToken)
    {
        if (string.IsNullOrWhiteSpace(value))
        {
            return _contacts.AsEnumerable();
        }

        await Task.Delay(5, cancellationToken);
        cancellationToken.ThrowIfCancellationRequested();

        return _contacts.Where(c =>
            $"{c.PRENOM} {c.NOM}".Contains(value, StringComparison.InvariantCultureIgnoreCase));
    }

    private async Task Save()
    {
        await form.Validate();

        if (success)
        {
            // Store individual string values
            await SessionStorage.SetItemAsync("telephone", telephone);
            await SessionStorage.SetItemAsync("prenom", prenom);
            await SessionStorage.SetItemAsync("nom", nom);
            await SessionStorage.SetItemAsync("compagnie", compagnie);

            // Store the selected contact
            if (_selectedContact != null)
            {
                await SessionStorage.SetItemAsync("selectedContact", _selectedContact);
            }
            else
            {
                var customContact = new Contact
                    {
                        ID_EMPLOYE = -1,
                        NOM = _customContactInput,
                    };
                await SessionStorage.SetItemAsync("selectedContact", customContact);
            }

            // Check if scanCode exists in session
            var scanCode = await SessionStorage.GetItemAsync<string>("scanCode");
            if (!string.IsNullOrWhiteSpace(scanCode))
            {
                var selectedArea = await SessionStorage.GetItemAsync<Lieu>("selectedArea");

                if (selectedArea?.requires_SQF == true)
                {
                    NavigationManager.NavigateTo("/SQF");
                }
                else
                {
                    NavigationManager.NavigateTo("/Signature");
                }

                return; // Stop here to avoid falling through
            }

            // Default navigation if not using scan
            NavigationManager.NavigateTo("/Lieux");
        }
    }


    private async Task NavigateToAccueil()
    {
        NavigationManager.NavigateTo("/");

    }


}