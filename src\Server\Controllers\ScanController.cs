﻿using Microsoft.AspNetCore.Mvc;
using Blazored.SessionStorage;
using VisitLog3.Models;
using PetaPoco.Providers;
using PetaPoco;
using System.Net.Mail;
using ProtegeGXService;
using System.ServiceModel;
using System.Xml.Linq;
using VisitLog3.Helpers;
using System.ComponentModel.DataAnnotations;
using Microsoft.AspNetCore.Connections.Features;
using VisitLog3.Components.Pages;
using System.Text.RegularExpressions;
using DocumentFormat.OpenXml.Office2010.PowerPoint;
using Microsoft.AspNetCore.SignalR;
using VisitLog3.Hubs;

namespace VisitLog3.Controllers
{
    [Route("api/scan")]
    [ApiController]
    public class ScanController : ControllerBase
    {
        private readonly IConfiguration _configuration;
        private readonly DatabaseFactory _dbFactory;
        private readonly IHubContext<VisitorLogHub> _hubContext;

        public ScanController(IConfiguration configuration, DatabaseFactory dbFactory, IHubContext<VisitorLogHub> hubContext)
        {
            _configuration = configuration;
            _dbFactory = dbFactory;
            _hubContext = hubContext;
        }

        [HttpGet("checkVisitorCards")]
        public async Task<IActionResult> checkVisitorCards()
        {
            try
            {
                var db = _dbFactory.GetDatabase("dbInformatique");

                var filteredCards = db.Fetch<lieu_carteAcces>(
                    "WHERE last_activation_time IS NOT NULL AND DATEDIFF(HOUR, last_activation_time, GETDATE()) > 12"
                );


                var connectionString = _configuration.GetConnectionString("dbInformatique");

                using (var dbDepart = new Database<SqlServerDatabaseProvider>(connectionString))
                {
                    foreach (var card in filteredCards)
                    {
                        try
                        {

                            var response = await DisableCard(_dbFactory, card.id_carte_acces, card) as ObjectResult;
                            int? gxErrorCode = null;

                            if (response?.Value != null)
                            {
                                var errorObj = response.Value;
                                var property = errorObj.GetType().GetProperty("code");
                                if (property != null)
                                {
                                    var value = property.GetValue(errorObj);
                                    if (value is long longValue)
                                    {
                                        gxErrorCode = Convert.ToInt32(longValue);
                                    }
                                }
                            }

                            if (gxErrorCode != null)
                            {
                                var errorMessage = dbDepart.SingleOrDefault<Gx_soap_api_errors>(
                                    "WHERE error_code = @0", gxErrorCode
                                )?.error_message;

                                var newVisitor = new Visiteur_Log
                                {
                                    scan = card.id_carte_acces,

                                    gx_error_code = gxErrorCode,

                                };
                                sendErrorEmail(
                                    "Automatic card deactivation failed: " + errorMessage,
                                    newVisitor,
                                    false
                                );
                                continue;
                            }
                        }
                        catch (Exception ex)
                        {
                        }
                    }
                }

                return Ok("All overdue visitor cards processed successfully.");
            }
            catch (Exception ex)
            {
                return StatusCode(500, new { success = false, message = ex.Message });
            }
        }

        [HttpPost("departure")]
        public async Task<IActionResult> Departure([FromBody] string scanResult)
        {
            if (string.IsNullOrEmpty(scanResult))
            {
                return BadRequest(new { success = false, message = "Scan result is empty or invalid data received." });
            }

            try
            {
                scanResult = scanResult.Trim();
                var connectionString = _configuration.GetConnectionString("dbInformatique");

                using (var dbDepart = new Database<SqlServerDatabaseProvider>(connectionString))
                {
                    var existingLog = dbDepart.SingleOrDefault<Visiteur_Log>(
                        "WHERE scan = @0 AND date_heure_depart IS NULL and deleted=0",
                        scanResult);

                    if (existingLog == null)
                    {
                        return BadRequest(new { success = false, message = "Error: No logged entry found for this card." });
                    }

                    var card = await dbDepart.SingleOrDefaultAsync<lieu_carteAcces>("WHERE id_carte_acces = @0", scanResult);

                    // Fire-and-forget DisableCard on another thread
                    _ = Task.Run(async () =>
                    {
                        try
                        {
                            var response = await DisableCard(_dbFactory, scanResult, card) as ObjectResult;
                            int? gxErrorCode = null;

                            if (response?.Value != null)
                            {
                                var errorObj = response.Value;
                                var property = errorObj.GetType().GetProperty("code");

                                if (property != null)
                                {
                                    var value = property.GetValue(errorObj);
                                    if (value is long longValue)
                                    {
                                        gxErrorCode = Convert.ToInt32(longValue);
                                    }
                                }
                            }

                            if (gxErrorCode != null)
                            {
                                using (var dbInner = new Database<SqlServerDatabaseProvider>(connectionString))
                                {
                                    var errorMessage = dbInner.SingleOrDefault<Gx_soap_api_errors>("WHERE error_code = @0", gxErrorCode)?.error_message;
                                    existingLog.gx_error_code = gxErrorCode;
                                    sendErrorEmail("Card deactivation failed: " + errorMessage, existingLog, false);
                                    dbInner.Update(existingLog);
                                }
                            }
                        }
                        catch (Exception ex)
                        {
                            Console.WriteLine($"Error in DisableCard: {ex.Message}");
                        }
                    });

                    existingLog.date_heure_depart = DateTime.Now;
                    dbDepart.Update(existingLog);

                    if (existingLog.contact_pex_id != -1)
                    {
                        await Task.Run(() => sendDepartureEmail(existingLog));
                    }

                    await _hubContext.Clients.All.SendAsync("UpdateVisitorDeparture", existingLog);
                }

                return Ok(new { success = true, message = "Goodbye! Departure recorded successfully." });
            }
            catch (Exception ex)
            {
                return StatusCode(500, new { success = false, message = $"Error: {ex.Message}" });
            }
        }

        private void sendErrorEmail(string errorMessage, Visiteur_Log visiteur, bool activation)
        {
            var pexConnectionString = _configuration.GetConnectionString("dbPEX");
            using (var pexDb = new Database<SqlServerDatabaseProvider>(pexConnectionString))
            {
                var alertMail = pexDb.SingleOrDefault<Application_Parametre>(
                    "WHERE APPLICATION = 'VISITLOG' AND CLE = 'alertMail'")?.Valeur;

                if (!string.IsNullOrEmpty(alertMail))
                {
                    try
                    {
                        using (var mail = new MailMessage())
                        {
                            mail.From = new MailAddress(alertMail);
                            mail.To.Add(alertMail);
                            mail.Subject = activation ? "Visitor Card Activation Error Alert" : "Visitor Card Deactivation Error Alert";
                            mail.Body = $@"
                                <p><strong>Error Message:</strong> {errorMessage}</p>
                                <p><strong>Visitor Information:</strong></p>
                                <ul>
                                    <li><strong>Name:</strong> {visiteur.prenom} {visiteur.nom}</li>
                                    <li><strong>Email:</strong> {visiteur.telephone}</li>
                                    <li><strong>Company:</strong> {visiteur.compagnie}</li>
                                    <li><strong>Contact Name:</strong> {visiteur.contact_pex_nom}</li>
                                    <li><strong>Scan Result:</strong> {visiteur.scan}</li>
                                    <li><strong>Entry Point:</strong> {visiteur.entry_point}</li>
                                    <li><strong>Error Code:</strong> {(visiteur.gx_error_code?.ToString() ?? "N/A")}</li>
                                    <li><strong>Arrival Time:</strong> {visiteur.date_heure_arrivee}</li>
                                </ul>";

                            mail.IsBodyHtml = true;

                            using (var smtp = new SmtpClient())
                            {
                                smtp.Host = "**************";
                                smtp.Port = 25;
                                smtp.Send(mail);
                            }
                        }
                    }
                    catch (Exception ex)
                    {
                        Console.WriteLine($"Failed to send error email: {ex.Message}");
                    }
                }
            }
        }


        private void sendDepartureEmail(Visiteur_Log existingLog)
        {
            try
            {
                var pexConnectionString = _configuration.GetConnectionString("dbPEX");
                using (var pexDb = new Database<SqlServerDatabaseProvider>(pexConnectionString))
                {
                    var contact = pexDb.SingleOrDefault<Contact>(
                        "WHERE ID_EMPLOYE = @0",
                        existingLog.contact_pex_id);

                    if (contact != null)
                    {
                        var contactEmail = contact.EMAIL;

                        using (var mail = new MailMessage())
                        {
                            mail.From = new MailAddress("<EMAIL>", "Notifications visiteurs");
                            mail.To.Add(contactEmail);

                            mail.Subject = $"Départ du visiteur {existingLog.prenom} {existingLog.nom}";
                            mail.Body = $@"
                                Bonjour,
                                Le visiteur suivant a quitté nos locaux :
                                - Prénom : {existingLog.prenom}
                                - Nom : {existingLog.nom}
                                - Compagnie : {existingLog.compagnie}
                                - Heure de départ : {existingLog.date_heure_depart:yyyy-MM-dd HH:mm:ss}
                            ";

                            using (var smtp = new SmtpClient())
                            {
                                smtp.Host = "**************";
                                smtp.Port = 25;

                                smtp.Send(mail);
                            }
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error while sending email: {ex.Message}");
            }
        }

        [HttpGet("reprocessGXError")]
        public async Task<IActionResult> reprocessGXError([FromQuery] string visitorID)
        {
            try
            {
                var db = _dbFactory.GetDatabase("dbInformatique");

                var visitor = await db.SingleOrDefaultAsync<Visiteur_Log>("WHERE id_visiteur_log = @0", visitorID);
                if (visitor == null)
                {
                    return NotFound(new { success = false, message = "Visitor log not found." });
                }

                var card = await db.SingleOrDefaultAsync<lieu_carteAcces>("WHERE id_carte_acces = @0", visitor.scan);
                if (card == null)
                {
                    return NotFound(new { success = false, message = "Associated card not found." });
                }

                ObjectResult? response = null;
                if (visitor.date_heure_depart == null)
                {
                    response = await ActivateCard(_dbFactory, visitor.scan, card) as ObjectResult;
                }
                else
                {
                    response = await DisableCard(_dbFactory, visitor.scan, card) as ObjectResult;
                }

                int? gxErrorCode = null;
                if (response?.Value != null)
                {
                    var errorObj = response.Value;
                    var property = errorObj.GetType().GetProperty("code");

                    if (property != null)
                    {
                        var value = property.GetValue(errorObj);
                        if (value is long longValue)
                        {
                            gxErrorCode = Convert.ToInt32(longValue);
                        }
                    }
                }

                if (gxErrorCode != null)
                {
                    var errorMessage = db.SingleOrDefault<Gx_soap_api_errors>("WHERE error_code = @0", gxErrorCode)?.error_message;

                    visitor.gx_error_code = gxErrorCode;

                    if (visitor.date_heure_depart == null)
                    {
                        db.Execute("UPDATE lieu_carteAcces SET last_activation_time = @0 WHERE id_carte_acces = @1",
                           DateTime.Now, card.id_carte_acces);
                    }

                }
                else
                {
                    visitor.gx_error_code = null;
                }

                db.Update("VISITEUR_LOG", "id_visiteur_log", visitor);

                return Redirect("/Admin");
            }
            catch (Exception ex)
            {
                return StatusCode(500, new { success = false, message = $"Error: {ex.Message}" });
            }
        }

        [HttpPost("entry")]
        public async Task<IActionResult> entry([FromBody] ScanRequestModel model)
        {
            if (model == null || string.IsNullOrEmpty(model.ScanResult))
            {
                return BadRequest(new { success = false, message = "Scan result is empty or invalid data received." });
            }

            try
            {
                // Unescape and trim data
                var scanResult = model.ScanResult is not null
                    ? Regex.Unescape(model.ScanResult.Trim('"'))
                    : null;

                var telephone = model.telephone is not null
                    ? Regex.Unescape(model.telephone.Trim('"'))
                    : null;

                var prenom = model.Prenom is not null
                    ? Regex.Unescape(model.Prenom.Trim('"'))
                    : null;

                var nom = model.Nom is not null
                    ? Regex.Unescape(model.Nom.Trim('"'))
                    : null;

                var compagnie = model.Compagnie is not null
                    ? Regex.Unescape(model.Compagnie.Trim('"'))
                    : null;

                var signatureBase64 = model.Base64Signature is not null
                    ? model.Base64Signature.Trim('"').Replace("\\u002B", "+")
                    : null;

                var lng = model.Lng is not null
                    ? Regex.Unescape(model.Lng.Trim('"'))
                    : null;

                var idLieu = model.SelectedArea;
                var contactID = model.ContactID;

                var contactEmail = model.ContactEmail is not null
                    ? Regex.Unescape(model.ContactEmail)
                    : null;

                var contactName = model.ContactName is not null
                    ? Regex.Unescape(model.ContactName)
                    : null;

                var entryPoint = model.EntryPoint is not null
                    ? Regex.Unescape(model.EntryPoint)
                    : null;

                var connectionString = _configuration.GetConnectionString("dbInformatique");

                using (var dbArrived = new Database<SqlServerDatabaseProvider>(connectionString))
                {
                    // --------------------------------------------------------------------------------
                    // 1) Handle "no scan" manually entered visitors (no card activation needed)
                    // --------------------------------------------------------------------------------
                    if (scanResult == "no scan")
                    {
                        var visitorLogNoScan = new Visiteur_Log
                        {
                            telephone = telephone,
                            prenom = prenom,
                            nom = nom,
                            compagnie = compagnie,
                            contact_pex_nom = contactName,
                            langue = lng,
                            scan = scanResult,  // "no scan"
                            contact_pex_id = contactID,
                            signature_png_base64 = signatureBase64,
                            date_heure_arrivee = DateTime.Now,
                            id_lieux = idLieu,
                            entry_point = entryPoint,
                            gx_error_code = null  // No card activation, so no error code
                        };

                        dbArrived.Insert(visitorLogNoScan);


                        if (contactEmail != null)
                        {
                            // Send email asynchronously, won't block the main thread
                            await Task.Run(() =>
                            {
                                sendEntryEmail(contactEmail, prenom, nom, compagnie, entryPoint);
                            });
                        }

                        return Ok(new { success = true, message = "Visitor entry recorded successfully (no scan).", isEntry = true });
                    }
                    var existingLog = dbArrived.SingleOrDefault<Visiteur_Log>(
                        "WHERE scan = @0 AND date_heure_depart IS NULL AND deleted = 0",
                        scanResult);

                    if (existingLog != null)
                    {
                        return BadRequest(new { success = false, message = "Error: This card is already logged as entering. Please log a departure first." });
                    }

                    var validCards = dbArrived.Fetch<lieu_carteAcces>("WHERE id_lieu = @0", idLieu);
                    var validCard = validCards.FirstOrDefault(card => card.id_carte_acces.ToString() == scanResult);

                    if (validCard == null)
                    {
                        return BadRequest(new { success = false, errorCode = "INVALID_AREA", message = "Error: This card does not grant access to the desired location." });
                    }

                    // Create a new log record for this visitor
                    var visitorWithScan = new Visiteur_Log
                    {
                        telephone = telephone,
                        prenom = prenom,
                        nom = nom,
                        compagnie = compagnie,
                        contact_pex_nom = contactName,
                        langue = lng,
                        scan = scanResult,
                        contact_pex_id = contactID,
                        signature_png_base64 = signatureBase64,
                        date_heure_arrivee = DateTime.Now,
                        id_lieux = idLieu,
                        entry_point = entryPoint,
                        gx_error_code = null // We set to null initially, might update if activation fails
                    };

                    // Insert the new visitor log now (to mark arrival immediately)
                    dbArrived.Insert(visitorWithScan);

                    await _hubContext.Clients.All.SendAsync("ReceiveVisitor", visitorWithScan);

                    // --------------------------------------------------------------------------------
                    // 3) Fire-and-forget card activation (doesn't block the current HTTP request)
                    // --------------------------------------------------------------------------------
                    _ = Task.Run(async () =>
                    {
                        try
                        {
                            // Attempt to activate card
                            var response = await ActivateCard(_dbFactory, scanResult, validCard) as ObjectResult;
                            int? gxErrorCode = null;

                            if (response?.Value != null)
                            {
                                var errorObj = response.Value;
                                var property = errorObj.GetType().GetProperty("code");

                                if (property != null)
                                {
                                    var value = property.GetValue(errorObj);
                                    if (value is long longValue)
                                    {
                                        gxErrorCode = Convert.ToInt32(longValue);
                                    }
                                }
                            }

                            // If there's an error code from Protege GX
                            if (gxErrorCode != null)
                            {
                                using (var dbInner = new Database<SqlServerDatabaseProvider>(connectionString))
                                {
                                    // Retrieve error message
                                    var errorMessage = dbInner.SingleOrDefault<Gx_soap_api_errors>(
                                        "WHERE error_code = @0", gxErrorCode)?.error_message;

                                    // Send an error email
                                    sendErrorEmail("Card activation failed: " + errorMessage, visitorWithScan, true);

                                    // Update the log record with the error code
                                    visitorWithScan.gx_error_code = gxErrorCode;
                                    dbInner.Update(visitorWithScan);
                                }
                            }
                            else
                            {
                                // If activation succeeded, update the last activation time
                                using (var dbInner = new Database<SqlServerDatabaseProvider>(connectionString))
                                {
                                    dbInner.Execute("UPDATE lieu_carteAcces SET last_activation_time = @0 WHERE id_carte_acces = @1",
                                                    DateTime.Now, validCard.id_carte_acces);
                                }
                            }
                        }
                        catch (Exception ex)
                        {
                            // Log or handle any exceptions that occur during card activation
                            Console.WriteLine($"Error in ActivateCard: {ex.Message}");
                        }
                    });

                    // --------------------------------------------------------------------------------
                    // 4) Send entry email if needed (doesn't block main thread)
                    // --------------------------------------------------------------------------------
                    if (contactEmail != null)
                    {
                        await Task.Run(() =>
                        {
                            sendEntryEmail(contactEmail, prenom, nom, compagnie, entryPoint);
                        });
                    }

                    // Return success immediately (card activation still running in the background)
                    return Ok(new { success = true, message = "Welcome! Entry recorded successfully.", isEntry = true });
                }
            }
            catch (Exception ex)
            {
                return StatusCode(500, new { success = false, message = $"Error: {ex.Message}" });
            }
        }


        private void sendEntryEmail(string contactEmail, string prenom, string nom, string compagnie, string entryPoint)
        {
            try
            {
                using (var mail = new MailMessage())
                {
                    mail.From = new MailAddress("<EMAIL>", "Notifications visiteurs");
                    mail.To.Add(contactEmail);
                    mail.Subject = $"Arrivé du visiteur {prenom} {nom}";
                    mail.Body = $@"
                        Bonjour,
                        Un visiteur à votre nom est arrivé :
                        - Prénom : {prenom}
                        - Nom : {nom}
                        - Compagnie : {compagnie}
                        - Heure d'arrivée : {DateTime.Now:yyyy-MM-dd HH:mm:ss}
                        - Point d'entrée : {(entryPoint ?? "Non spécifié")}
                    ";

                    using (var smtp = new SmtpClient())
                    {
                        smtp.Host = "**************";
                        smtp.Port = 25;
                        smtp.Send(mail);
                    }
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Failed to send email: {ex.Message}");
            }
        }


        public async Task<IActionResult> DisableCard([FromServices] DatabaseFactory dbFactory, string scanResult, lieu_carteAcces card)
        {
            int tableId = 100;
            int parentId = 1;
            int recordId = card.gx_user_id;
            var parts = scanResult.Split('-');

            if (parts.Length != 2)
            {
                return BadRequest(new { success = false, message = "Invalid scan result format." });
            }

            if (!int.TryParse(parts[0], out int familyNumber))
            {
                return BadRequest(new { success = false, message = "Invalid family number format." });
            }

            if (!int.TryParse(parts[1].TrimStart('0'), out int cardNumber))
            {
                return BadRequest(new { success = false, message = "Invalid card number format." });
            }

            try
            {
                using var db = dbFactory.GetDatabase("dbInformatique");
                var gxUser = await db.FirstOrDefaultAsync<dynamic>(
                    "SELECT TOP 1 user_name, password FROM dbo.SOAP_API_GX_USERS");

                if (gxUser == null)
                {
                    return NotFound(new { code = 1001, message = "No user credentials found." });
                }

                string originalXml;
                var bindingGet = new BasicHttpBinding
                {
                    Security = { Mode = BasicHttpSecurityMode.None },
                    MaxReceivedMessageSize = int.MaxValue,
                    MaxBufferPoolSize = int.MaxValue,
                    ReaderQuotas = System.Xml.XmlDictionaryReaderQuotas.Max
                };
                var endpointGet = new EndpointAddress("http://vmgx01.polyexpert.com:8030/ProtegeGXSOAPService/service.svc");
                using (var clientGet = new Service1Client(bindingGet, endpointGet))
                {
                    var logon = new Logon
                    {
                        LogonType = 0,
                        UserName = gxUser.user_name,
                        Password = gxUser.password
                    };

                    var getRequest = new GetRecordRequest
                    {
                        LD = logon,
                        nTableID = tableId,
                        nParentID = parentId,
                        nRecordID = recordId,
                        strXML = string.Empty,
                        nErrorCode = 0,
                        strErrorXML = string.Empty
                    };

                    var getResponse = await clientGet.GetRecordAsync(getRequest);
                    if (!getResponse.GetRecordResult)
                        return BadRequest(new { code = getResponse.nErrorCode, message = getResponse.strErrorXML });

                    originalXml = getResponse.strXML;
                }

                string minimalXml = BuildMinimalUserCardXml(originalXml, recordId, 1, cardNumber, familyNumber, activate: false);

                var bindingUp = new BasicHttpBinding
                {
                    Security = { Mode = BasicHttpSecurityMode.None },
                    MaxReceivedMessageSize = int.MaxValue,
                    MaxBufferPoolSize = int.MaxValue,
                    ReaderQuotas = System.Xml.XmlDictionaryReaderQuotas.Max
                };
                var endpointUp = new EndpointAddress("http://vmgx01.polyexpert.com:8030/ProtegeGXSOAPService/service.svc");
                using (var clientUp = new Service1Client(bindingUp, endpointUp))
                {
                    var logon2 = new Logon
                    {
                        LogonType = 0,
                        UserName = gxUser.user_name,
                        Password = gxUser.password
                    };

                    var updateRequest = new UpdateRecordRequest
                    {
                        LD = logon2,
                        nTableID = tableId,
                        nParentID = parentId,
                        nRecordID = recordId,
                        strXML = minimalXml,
                        nErrorCode = 0,
                        strErrorXML = string.Empty
                    };

                    var updateResponse = await clientUp.UpdateRecordAsync(updateRequest);
                    // after UpdateRecordAsync confirms success

                    if (!updateResponse.UpdateRecordResult)
                    {
                        return BadRequest(new
                        {
                            code = updateResponse.nErrorCode,
                            message = updateResponse.strErrorXML
                        });
                    }
                }

                return Ok(new { code = 0, message = "Card disabled successfully via GET." });
            }
            catch (Exception ex)
            {
                return StatusCode(StatusCodes.Status500InternalServerError,
                    new { code = -1, message = ex.Message });
            }
        }

        public async Task<IActionResult> ActivateCard([FromServices] DatabaseFactory dbFactory, string scanResult, lieu_carteAcces card)
        {
            int tableId = 100;
            int parentId = 1;
            int recordId = card.gx_user_id;
            var parts = scanResult.Split('-');

            if (parts.Length != 2)
            {
                return BadRequest(new { success = false, message = "Invalid scan result format." });
            }

            if (!int.TryParse(parts[0], out int familyNumber))
            {
                return BadRequest(new { success = false, message = "Invalid family number format." });
            }

            if (!int.TryParse(parts[1].TrimStart('0'), out int cardNumber))
            {
                return BadRequest(new { success = false, message = "Invalid card number format." });
            }

            try
            {
                using var db = dbFactory.GetDatabase("dbInformatique");
                var gxUser = await db.FirstOrDefaultAsync<dynamic>(
                    "SELECT TOP 1 user_name, password FROM dbo.SOAP_API_GX_USERS");

                if (gxUser == null)
                {
                    return NotFound(new { code = 1001, message = "No user credentials found." });
                }

                string originalXml;
                var bindingGet = new BasicHttpBinding
                {
                    Security = { Mode = BasicHttpSecurityMode.None },
                    MaxReceivedMessageSize = int.MaxValue,
                    MaxBufferPoolSize = int.MaxValue,
                    ReaderQuotas = System.Xml.XmlDictionaryReaderQuotas.Max
                };

                var endpointGet = new EndpointAddress("http://vmgx01.polyexpert.com:8030/ProtegeGXSOAPService/service.svc");
                using (var clientGet = new Service1Client(bindingGet, endpointGet))
                {
                    var logon = new Logon
                    {
                        LogonType = 0,
                        UserName = gxUser.user_name,
                        Password = gxUser.password
                    };

                    var getRequest = new GetRecordRequest
                    {
                        LD = logon,
                        nTableID = tableId,
                        nParentID = parentId,
                        nRecordID = recordId,
                        strXML = string.Empty,
                        nErrorCode = 0,
                        strErrorXML = string.Empty
                    };

                    var getResponse = await clientGet.GetRecordAsync(getRequest);
                    if (!getResponse.GetRecordResult)
                        return BadRequest(new { code = getResponse.nErrorCode, message = getResponse.strErrorXML });

                    originalXml = getResponse.strXML;
                }

                string minimalXml = BuildMinimalUserCardXml(originalXml, recordId, 1, cardNumber, familyNumber, activate: true);

                var bindingUp = new BasicHttpBinding
                {
                    Security = { Mode = BasicHttpSecurityMode.None },
                    MaxReceivedMessageSize = int.MaxValue,
                    MaxBufferPoolSize = int.MaxValue,
                    ReaderQuotas = System.Xml.XmlDictionaryReaderQuotas.Max
                };
                var endpointUp = new EndpointAddress("http://vmgx01.polyexpert.com:8030/ProtegeGXSOAPService/service.svc");
                using (var clientUp = new Service1Client(bindingUp, endpointUp))
                {
                    var logon2 = new Logon
                    {
                        LogonType = 0,
                        UserName = gxUser.user_name,
                        Password = gxUser.password
                    };

                    var updateRequest = new UpdateRecordRequest
                    {
                        LD = logon2,
                        nTableID = tableId,
                        nParentID = parentId,
                        nRecordID = recordId,
                        strXML = minimalXml,
                        nErrorCode = 0,
                        strErrorXML = string.Empty
                    };

                    var updateResponse = await clientUp.UpdateRecordAsync(updateRequest);


                    if (!updateResponse.UpdateRecordResult)
                    {
                        return BadRequest(new
                        {
                            code = updateResponse.nErrorCode,
                            message = updateResponse.strErrorXML
                        });
                    }
                }

                return Ok(new { code = 0, message = "Card activated successfully via GET." });
            }
            catch (Exception ex)
            {
                return StatusCode(StatusCodes.Status500InternalServerError,
                    new { code = -1, message = ex.Message });
            }
        }

        private string BuildMinimalUserCardXml(
            string originalFullXml,
            int recordId,
            int siteId,
            int cardNumber,
            int familyNumber,
            bool activate)
        {
            var originalDoc = XDocument.Parse(originalFullXml);

            var userCardNumberGroup = originalDoc.Root?.Element("UserCardNumberGroupData");

            if (userCardNumberGroup == null)
            {
                return $@"<?xml version=""1.0""?>
            <UsersData>
              <Index>{recordId}</Index>
              <SiteID>{siteId}</SiteID>
            </UsersData>";
            }

            var newDoc = new XDocument(
                new XElement("UsersData",
                    new XElement("Index", recordId),
                    new XElement("SiteID", siteId),

                    new XElement("UserCardNumberGroupData",
                        userCardNumberGroup.Elements("UserCardNumberGroupDataData")
                        .Select(cardElem => {

                            var cloned = new XElement(cardElem);

                            var cnVal = cloned.Element("CardNumber")?.Value;
                            var fnVal = cloned.Element("FamilyNumber")?.Value;

                            if (cnVal == cardNumber.ToString() && fnVal == familyNumber.ToString())
                            {
                                var disabledElem = cloned.Element("CardDisabled");
                                if (disabledElem != null)
                                    disabledElem.Value = activate ? "false" : "true";
                            }

                            return cloned;
                        })
                    )
                )
            );

            return newDoc.ToString();
        }
    }

}
