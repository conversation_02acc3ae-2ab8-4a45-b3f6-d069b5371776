﻿
<MudTextField T="DateTime?"
              Format="s"
              InputType="InputType.DateTimeLocal"
              @bind-Value="Departure"
              Max="9999-12-31T23:59"
              OnBlur="HandleCommit" />

@code {

    [Parameter]
    [EditorRequired]
    public CellContext<Visiteur_Log> Context { get; set; } = default!;

    [CascadingParameter]
    public MudDataGrid<Visiteur_Log> DataGrid { get; set; } = default!;

    private DateTime? Departure
    {
        get => Context.Item.date_heure_depart;
        set
        {
            // Only update the value if it changes
            if (Context.Item.date_heure_depart != value)
            {
                Context.Item.date_heure_depart = value;
            }
        }
    }

    private async Task HandleCommit()
    {
        await Task.Delay(500);
        await DataGrid.CommittedItemChanges.InvokeAsync(Context.Item);
    }
}
