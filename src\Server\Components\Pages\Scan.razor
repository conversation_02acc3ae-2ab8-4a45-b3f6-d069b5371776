@page "/Scan"
@page "/Scan/{mode?}"

@rendermode InteractiveServer
@inject Blazored.SessionStorage.ISessionStorageService SessionStorage
@inject NavigationManager Navigation
@inject IJSRuntime JSRuntime
@inject ISnackbar Snackbar
@inject IConfiguration Configuration
@inject IHttpContextAccessor HttpContextAccessor
@inject IHttpClientFactory ClientFactory
@inject Microsoft.AspNetCore.Components.Server.ProtectedBrowserStorage.ProtectedSessionStorage ProtectedSessionStorage

@using System.Text.Json



<MudButton id="alertError" OnClick="@this.ShowMessageError"
Color="Color.Success" style="display: none;">
    Show message
</MudButton>

<MudButton id="alertErrorSelectedArea" OnClick="@this.ShowMessageErrorWrongSelectedArea"
           Color="Color.Success" style="display: none;">
    Show message
</MudButton>

<MudButton id="alertDepartureSuccess" OnClick="@this.ShowMessageDepartureSuccess"
Color="Color.Success" style="display: none;">
    Show message
</MudButton>

<MudButton id="alertArrivalSuccess" OnClick="@this.ShowMessageArrivalSuccess"
Color="Color.Success" style="display: none;">
    Show message
</MudButton>

<script>
    window.cameraUtils = {
        getVideoDevices: async function () {
            try {
                // Request camera access to unlock labels and deviceIds
                await navigator.mediaDevices.getUserMedia({ video: true });

                const devices = await navigator.mediaDevices.enumerateDevices();
                const videoDevices = devices.filter(device => device.kind === 'videoinput');

                return videoDevices.map(device => ({
                    label: device.label,
                    deviceId: device.deviceId
                }));
            } catch (err) {
                console.error("Camera access error:", err);
                return [];
            }
        },
    stopAllVideo: function () {
      const vids = document.querySelectorAll('video');
      vids.forEach(v => {
        const s = v.srcObject;
        if (s && typeof s.getTracks === 'function') {
          s.getTracks().forEach(t => t.stop());
          v.srcObject = null;
        }
      });
    }
  };
</script>


<div class="container top-16">
    <div class="col-7 custom-offset">
        <div class="d-grid gap-5">
            <div class="d-flex justify-content-between align-items-center py-3">
                <button id="goBackButton" class="secondary-btn d-flex align-items-center justify-content-center py-2 px-4"
                aria-label="Go Back" @onclick="NavigateBack">
                    <img src="/images/arrow-left-solid.png" alt="House Icon" style="width: 20px; height: 20px;">
                </button>

                <h4 id="instructionsHeader" class="primary-header text-center my-0">
                    @GetHeaderText()
                </h4>

                <button id="goHomeButton" class="secondary-btn d-flex align-items-center justify-content-center py-2 px-4"
                aria-label="Go to Home" @onclick="NavigateToHome">
                    <img src="/images/house-white.png" alt="House Icon" style="width: 20px; height: 20px;">
                </button>
            </div>

        </div>

        @if (IsEntry)
        {
            <MudStack Justify="Justify.Center">
                <MudPaper Class="pa-3 ma-1 d-flex justify-center align-center" Elevation="1" Style="color: rgb(101, 115, 147);">
                    <MudText Typo="Typo.h4">
                        @if (lng == "en")
                        {
                            @selectedArea?.nom_en
                        }
                        else
                        {
                            @selectedArea?.nom_fr
                        }

                    </MudText>
                </MudPaper>

                <MudStack Row="true" Justify="Justify.SpaceBetween" AlignItems="AlignItems.Center">
                    <MudStack Row="true" Justify="Justify.FlexStart" AlignItems="AlignItems.Start">
                        @if (!string.IsNullOrEmpty(visitorLastName) || !string.IsNullOrEmpty(visitorFirstName))
                        {
                            <MudPaper Class="pa-2" Style="color: rgb(101, 115, 147);">@($"{visitorFirstName} {visitorLastName}".Trim())</MudPaper>
                        }

                        @if (!string.IsNullOrEmpty(company))
                        {
                            <MudPaper Class="pa-2" Style="color: rgb(101, 115, 147);">@company</MudPaper>
                        }
                    </MudStack>

                    <MudStack Row="true" Justify="Justify.FlexEnd" AlignItems="AlignItems.End">
                        @if (contact != null && (!string.IsNullOrEmpty(contact.NOM) || !string.IsNullOrEmpty(contact.PRENOM)))
                        {
                            <MudPaper Class="pa-2" Style="color: rgb(101, 115, 147);">@($"{contact.PRENOM} {contact.NOM}".Trim())</MudPaper>
                        }
                    </MudStack>
                </MudStack>
            </MudStack>
        }

        <div style="position: relative; width: 100%; height: auto; margin-top: 20px; display: flex; flex-direction: column; align-items: center;">
            <!-- Centered Button -->
            <button id="cameraSwitchBtn" class="d-flex align-items-center justify-content-center py-2 px-4"
            aria-label="flip camera"
                    hidden=@(!AllowCameraToggle)
            style="position: absolute; top: 10px; left: 50%; transform: translateX(-50%); z-index: 100"
            @onclick="ToggleCamera">
                <img src="/images/camera-rotate-solid.png" style="width: 25px; height: 20px;">
            </button>

            <!-- Scanner Video Container -->
            <div class="scanner-container">
                <QRCodeScanner LoadingMessage="" Width="39%"/>
                <div class="overlay"></div>
                <img src="/images/hd-blue-laser-beam.png" class="laser-image" alt="Scanning Laser" />
            </div>

            <!-- Proceed Button (Now Positioned Below the Scanner) -->
            <button id="proceedWithoutCardButton" type="button" @onclick="ProceedWithoutCard"
            class="secondary-btn py-2 mt-3" hidden>
                @(lng == "en" ? "Proceed without Card" : "Procéder sans carte")
                <img src="/images/arrow-right-solid.png" style="height: 20px;">
            </button>
        </div>

    </div>
</div>

<style>
    body {
    overflow: hidden;
    }
</style>


@code {

    private bool isInitialized = false;
    private string lng = string.Empty;
    private Lieu? selectedArea;
    private string? visitorLastName;
    private string? visitorFirstName;
    private string? email;
    private string? company;
    private Contact? contact;
    private QRCodeScannerJsInterop? _qrCodeScannerJsInterop;
    private Action<string>? _onQrCodeScanAction;
    private bool _useFrontCamera = false;

    private string? _scanMode;
    private bool IsEntry => _scanMode == "Entry";
    private bool IsRecurring => _scanMode == "Recurring";
    private bool IsDeparture => _scanMode == "Departure";
    private bool AllowCameraToggle => !IsEntry || IsReception;
    private string? entryPoint;
    private bool IsReception => string.Equals(entryPoint?.Trim(), "Reception", StringComparison.OrdinalIgnoreCase);


    private bool scannerReady = false;

    [Parameter]
    [SupplyParameterFromQuery]
    public string? Mode { get; set; }

    protected override async Task OnAfterRenderAsync(bool firstRender)
    {
        if (firstRender)
        {
            entryPoint = HttpContextAccessor.HttpContext?.Request.Cookies["location"];

            var ctx = HttpContextAccessor.HttpContext;
            _scanMode = ctx?.Session.GetString("ScanMode");

            if (string.IsNullOrEmpty(_scanMode))
            {
                // Someone typed /Scan directly, or ticket expired
                Navigation.NavigateTo("/");
                return;
            }

            _useFrontCamera = !IsEntry;   // Entry = rear cam, others = front

            visitorLastName = await SessionStorage.GetItemAsync<string>("nom");
            visitorFirstName = await SessionStorage.GetItemAsync<string>("prenom");
            email = await SessionStorage.GetItemAsync<string>("telephone");
            company = await SessionStorage.GetItemAsync<string>("compagnie");
            contact = await SessionStorage.GetItemAsync<Contact>("selectedContact");
            selectedArea = await SessionStorage.GetItemAsync<Lieu>("selectedArea");
            lng = await SessionStorage.GetItemAsync<string>("lng") ?? "en";

            isInitialized = true;
            _onQrCodeScanAction = (code) => OnQrCodeScan(code);
            _qrCodeScannerJsInterop = new QRCodeScannerJsInterop(JSRuntime);

            await DetectCameras();

            // Get allowed camera device IDs from DB
            var connectionString = Configuration.GetConnectionString("dbInformatique");
            List<string> allowedDeviceIds;
            using (var db = new PetaPoco.Database(connectionString, "System.Data.SqlClient"))
            {
                allowedDeviceIds = db.Fetch<string>("SELECT DeviceId FROM CAMERA_DEVICES");
            }

            // Check if any connected camera matches the allowed list
            bool hasValidCamera = ConnectedCameras.Any(c => allowedDeviceIds.Contains(c.DeviceId));

            if (hasValidCamera)
            {
                if (IsEntry)
                    await _qrCodeScannerJsInterop.Init(_onQrCodeScanAction, useFrontCamera: false);
                else
                    await _qrCodeScannerJsInterop.Init(_onQrCodeScanAction, useFrontCamera: true);
            }
            else
            {
                Snackbar.Configuration.PositionClass = Defaults.Classes.Position.BottomRight;
                Snackbar.Configuration.IconSize = Size.Large;
                Snackbar.Add("A valid rear camera is not connected.", Severity.Error);
            }

            StateHasChanged();
        }
    }


    private async Task OnQrCodeScan(string code)
    {
        if (_qrCodeScannerJsInterop is not null)
        {
            await _qrCodeScannerJsInterop.StopRecording();
        }

        try
        {
            if (Mode == "recurring")
            {
                var connectionString = Configuration.GetConnectionString("dbInformatique");
                using var db = new PetaPoco.Database(connectionString, "System.Data.SqlClient");

                var linkQuery = "SELECT id_lieu, recurrent FROM lieu_carteAcces WHERE id_carte_acces = @0";
                var linkResult = await db.FirstOrDefaultAsync<dynamic>(linkQuery, code);

                if (linkResult != null && linkResult.recurrent == true)
                {
                    var lieu = await db.FirstOrDefaultAsync<Lieu>(
                        "SELECT * FROM Lieux WHERE id_lieux = @0", linkResult.id_lieu);

                    if (lieu != null)
                        await SessionStorage.SetItemAsync("selectedArea", lieu);

                    var logQuery = @"
                    SELECT TOP 1 *
                    FROM visiteur_log
                    WHERE scan = @0 AND deleted = 0
                    ORDER BY date_heure_arrivee DESC";

                    var log = await db.FirstOrDefaultAsync<Visiteur_Log>(logQuery, code);

                    await SessionStorage.SetItemAsync("scanCode", code);

                    if (log != null)
                    {
                        await SessionStorage.SetItemAsync("telephone", log.telephone ?? string.Empty);
                        await SessionStorage.SetItemAsync("prenom", log.prenom ?? string.Empty);
                        await SessionStorage.SetItemAsync("nom", log.nom ?? string.Empty);
                        await SessionStorage.SetItemAsync("compagnie", log.compagnie ?? string.Empty);

                        if (log.contact_pex_id >= 0)
                        {
                            var pexConn = Configuration.GetConnectionString("dbPEX");
                            using var pexDb = new PetaPoco.Database(pexConn, "System.Data.SqlClient");

                            var contact = await pexDb.FirstOrDefaultAsync<Contact>(
                                "SELECT TOP 1 * FROM EMPLOYE WHERE ID_EMPLOYE = @0",
                                log.contact_pex_id);

                            if (contact != null)
                                await SessionStorage.SetItemAsync("selectedContact", contact);
                        }
                    }

                    Navigation.NavigateTo("/Identification");
                    return;
                }
                else
                {
                    Snackbar.Configuration.PositionClass = Defaults.Classes.Position.BottomRight;
                    Snackbar.Configuration.IconSize = Size.Large;
                    Snackbar.Add("Carte non valide ou non marquée comme récurrente.", Severity.Error);
                }
            }
            else if (IsDeparture)
            {
                var ok = await SendDepartureAsync(code);
                if (ok) return;
            }
            else // Entry
            {
                var ok = await SendArrivalAsync(code);
                if (ok) return;
            }
        }
        catch (Exception ex)
        {
            Snackbar.Configuration.PositionClass = Defaults.Classes.Position.BottomRight;
            Snackbar.Add($"Erreur inattendue: {ex.Message}", Severity.Error);
        }

        // 🟢 If we reach here, it failed — restart scanner
        if (_qrCodeScannerJsInterop is not null)
        {
            await _qrCodeScannerJsInterop.Init(_onQrCodeScanAction, _useFrontCamera);
        }
    }


    // -------------------------------------------------
    //  SendDepartureAsync
    // -------------------------------------------------
    private async Task<bool> SendDepartureAsync(string scanCode)
    {
        await _qrCodeScannerJsInterop.StopRecording();
        var departureUrl = new Uri(new Uri(Navigation.BaseUri), "api/scan/departure");

        var client = ClientFactory.CreateClient();

        using var resp = await client.PostAsJsonAsync(departureUrl, scanCode);

        if (resp.IsSuccessStatusCode)
        {
            await ShowMessageDepartureSuccess();
            return true;
        }

        var content = await resp.Content.ReadAsStringAsync();
        using var doc = JsonDocument.Parse(content);
        var root = doc.RootElement;

        var message = root.GetProperty("message").GetString();
        var errorCode = root.TryGetProperty("errorCode", out var codeElement) ? $" (Code: {codeElement.GetString()})" : "";

        Snackbar.Configuration.PositionClass = Defaults.Classes.Position.BottomRight;

        Snackbar.Add(message, Severity.Error);
        return false;
    }


    private async Task<bool> SendArrivalAsync(string scanCode)
    {
        // 1. Try to retrieve the protected signature
        var sigResult = await ProtectedSessionStorage.GetAsync<string>("base64Signature");

        // 2. Fail early if the user deleted or tampered with it
        if (!sigResult.Success || string.IsNullOrWhiteSpace(sigResult.Value))
        {
            Snackbar.Configuration.PositionClass = Defaults.Classes.Position.BottomRight;
            Snackbar.Add("Signature missing or tampered with. Please sign again.", Severity.Error);
            return false;
        }

        var selectedContact = await SessionStorage.GetItemAsync<Contact>("selectedContact");
        var selectedArea = await SessionStorage.GetItemAsync<Lieu>("selectedArea");

        // 3. Build the payload with the *validated* signature
        var payload = new
        {
            scanResult = scanCode,
            telephone = await SessionStorage.GetItemAsync<string>("telephone"),
            prenom = await SessionStorage.GetItemAsync<string>("prenom"),
            nom = await SessionStorage.GetItemAsync<string>("nom"),
            compagnie = await SessionStorage.GetItemAsync<string>("compagnie"),
            base64Signature = sigResult.Value,          // <-- safe to use
            lng = await SessionStorage.GetItemAsync<string>("lng"),
            selectedArea = selectedArea?.id_lieux,
            contactID = selectedContact?.ID_EMPLOYE,
            contactEmail = selectedContact?.EMAIL,
            contactName = $"{selectedContact?.NOM} {selectedContact?.PRENOM}",
            entryPoint = HttpContextAccessor.HttpContext?.Request.Cookies["location"]
        };

        var entryUrl = new Uri(new Uri(Navigation.BaseUri), "api/scan/entry");
        var client = ClientFactory.CreateClient();

        using var resp = await client.PostAsJsonAsync(entryUrl, payload);

        if (resp.IsSuccessStatusCode)
        {
            await ShowMessageArrivalSuccess();
            return true;
        }

        var content = await resp.Content.ReadAsStringAsync();

        try
        {
            using var doc = JsonDocument.Parse(content);
            var root = doc.RootElement;

            var message = root.GetProperty("message").GetString();
            var errorCode = root.TryGetProperty("errorCode", out var codeElement) ? $" (Code: {codeElement.GetString()})" : "";

            Snackbar.Configuration.PositionClass = Defaults.Classes.Position.BottomRight;

            Snackbar.Add(message, Severity.Error);
        }
        catch
        {
            Snackbar.Configuration.PositionClass = Defaults.Classes.Position.BottomRight;
            Snackbar.Add($"Erreur d’arrivée: {content}", Severity.Error);
        }

        return false;
    }

    async Task ShowMessageArrivalSuccess()
    {
        this.Snackbar.Configuration.PositionClass = Defaults.Classes.Position.BottomRight;
        this.Snackbar.Configuration.IconSize = Size.Large;
        this.Snackbar.Add("Arrivée enregistrée", Severity.Success);

        if (_qrCodeScannerJsInterop is not null)
        {
            await _qrCodeScannerJsInterop.StopRecording();
        }

        Navigation.NavigateTo("/");
    }

    async Task ShowMessageDepartureSuccess()
    {
        this.Snackbar.Configuration.PositionClass = Defaults.Classes.Position.BottomRight;
        this.Snackbar.Configuration.IconSize = Size.Large;
        this.Snackbar.Add("Départ enregistré", Severity.Success);

        if (_qrCodeScannerJsInterop is not null)
        {
            await _qrCodeScannerJsInterop.StopRecording();
        }

        Navigation.NavigateTo("/");
    }


    void ShowMessageError()
    {
        this.Snackbar.Configuration.PositionClass = Defaults.Classes.Position.BottomRight;
        this.Snackbar.Configuration.IconSize = Size.Large;
        this.Snackbar.Add("Carte invalide", Severity.Error);
    }

    void ShowMessageErrorWrongSelectedArea()
    {
        this.Snackbar.Configuration.PositionClass = Defaults.Classes.Position.BottomRight;
        this.Snackbar.Configuration.IconSize = Size.Large;
        this.Snackbar.Add("Lieu de visite incorrect. Veuillez revenir en arrière et sélectionner le bon lieu.", Severity.Error);
    }

    private string GetHeaderText()
    {
        if (selectedArea == null)
        {
            return "Numériser carte d'accès / Scan access card";
        }

        return lng == "en"
            ? "Please scan your access card for"
            : "Veuillez numériser votre carte d'accès pour";
    }

    private async Task NavigateBack()
    {
        await StopScannerAndCameraAsync();

        var selectedArea = await SessionStorage.GetItemAsync<Lieu>("selectedArea");

        var url = selectedArea switch
        {
            null => "/",
            { requires_SQF: true } => "/SQF",
            _ => "/Lieux"
        };

        Navigation.NavigateTo(url);
    }



    private async Task  NavigateToHome()
    {
        await StopScannerAndCameraAsync();

        Navigation.NavigateTo("/");
    }

    private async Task ProceedWithoutCard()
    {
        try
        {
            await _qrCodeScannerJsInterop.StopRecording();
            await JSRuntime.InvokeVoidAsync("proceedWithoutCard");

        }
        catch (Exception ex)
        {
            Snackbar.Add($"Error calling JavaScript function: {ex.Message}", Severity.Error);
        }
    }

    private async Task StopScannerAndCameraAsync()
{

    await _qrCodeScannerJsInterop.StopRecording();

    await JSRuntime.InvokeVoidAsync("cameraUtils.stopAllVideo");
}


    private async Task ToggleCamera()
    {
        if (!AllowCameraToggle || _qrCodeScannerJsInterop is null) return;
            await _qrCodeScannerJsInterop.StopRecording();
            _useFrontCamera = !_useFrontCamera;
            await _qrCodeScannerJsInterop.Init(_onQrCodeScanAction, _useFrontCamera);

    }

    public record CameraDevice(string Label, string DeviceId);

    private List<CameraDevice> ConnectedCameras = new();

    private async Task DetectCameras()
    {
        try
        {
            var cameras = await JSRuntime.InvokeAsync<CameraDevice[]>("cameraUtils.getVideoDevices");
            ConnectedCameras = cameras.ToList();
            Console.WriteLine($"Detected {ConnectedCameras.Count} camera(s):");
            foreach (var cam in ConnectedCameras)
                Console.WriteLine($"- {cam.Label}");
        }
        catch (Exception ex)
        {
            Snackbar.Add($"Erreur de détection de caméra : {ex.Message}", Severity.Error);
        }
    }
}