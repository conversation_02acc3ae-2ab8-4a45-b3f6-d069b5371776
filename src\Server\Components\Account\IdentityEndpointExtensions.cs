﻿using Microsoft.AspNetCore.Authentication;
using Microsoft.AspNetCore.Identity;
using Microsoft.AspNetCore.Mvc;
using System.Security.Claims;

namespace Microsoft.AspNetCore.Routing;

internal static class IdentityEndpointExtensions
{
    public static IEndpointConventionBuilder MapIdentityEndpoints(this IEndpointRouteBuilder endpoints)
    {
        ArgumentNullException.ThrowIfNull(endpoints);

        var accountGroup = endpoints.MapGroup("/Account");

        accountGroup.MapGet("/Logout", async (HttpContext context) =>
        {
            await context.SignOutAsync("TdtsCookie");

            var request = context.Request;
            var host = request.Host.Value;
            var scheme = request.Scheme;

            var postLogoutRedirectUri = $"{scheme}://{host}/";

            var microsoftLogoutUrl = "https://login.microsoftonline.com/c57678af-2274-48d1-a2ab-882a1ed3a4df/oauth2/v2.0/logout" +
                                     "?post_logout_redirect_uri=" +
                                     Uri.EscapeDataString(postLogoutRedirectUri);

            return Results.Redirect(microsoftLogoutUrl);
        });

        accountGroup.MapPost("/PerformGoogleLogin", async (
            HttpContext context,
            [FromForm] string returnUrl
        ) =>
        {
            // a more generic implementation would pass the provider as well but we know it's Google here
            string provider = "Google";

            var properties = new AuthenticationProperties { RedirectUri = returnUrl };

            // Sign out any existing user
            await context.SignOutAsync("TdtsCookie");

            return TypedResults.Challenge(properties, [provider]);
        });

        accountGroup.MapPost("/PerformMicrosoftLogin", async (
             HttpContext context,
             [FromForm] string returnUrl
             ) =>
        {
            // a more generic implementation would pass the provider as well but we know it's Google here
            string provider = "Microsoft";

            var properties = new AuthenticationProperties { RedirectUri = returnUrl };

            // Sign out any existing user
            await context.SignOutAsync("TdtsCookie");

            return TypedResults.Challenge(properties, [provider]);
        });

        return accountGroup;
    }
}

