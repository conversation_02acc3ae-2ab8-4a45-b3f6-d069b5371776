﻿using VisitLog3.Components;
using Microsoft.AspNetCore.Components.Authorization;
using Microsoft.AspNetCore.Components.Server;
using Blazored.SessionStorage;
using MudBlazor.Services;
using VisitLog3.Services;
using MudBlazor;
using VisitLog3.Helpers;
using VisitLog3.Hubs;
using Microsoft.AspNetCore.Components;
using Microsoft.AspNetCore.Antiforgery;

var builder = WebApplication.CreateBuilder(args);

builder.Services.AddRazorComponents().AddInteractiveServerComponents();

builder.Services.AddScoped<AuthenticationStateProvider, ServerAuthenticationStateProvider>();

builder.Services.AddAuthorization(options =>
{
    options.AddPolicy("CanLogin", policy =>
    {
        policy.RequireRole("CanLogin");
    });
});

builder.Services.AddAuthentication("TdtsCookie")
    .AddCookie("TdtsCookie")
    .AddMicrosoftAccount(options =>
    {
        options.ClientId = builder.Configuration["Authentication:Microsoft:ClientId"];
        options.ClientSecret = builder.Configuration["Authentication:Microsoft:ClientSecret"];
        options.AuthorizationEndpoint = builder.Configuration["Authentication:Microsoft:AuthorizationEndpoint"];
        options.TokenEndpoint = builder.Configuration["Authentication:Microsoft:TokenEndpoint"];
        options.SaveTokens = true;

    });

builder.Services.AddHttpClient();

builder.Services.AddBlazoredSessionStorage();

builder.Services.AddMudServices();

builder.Services.AddSignalR(e => {
    e.MaximumReceiveMessageSize = *********;
});

builder.Services.AddSingleton<SerAppSession>();

builder.Services
    .AddServerSideBlazor()
    .AddHubOptions(o =>
    {
        o.KeepAliveInterval = TimeSpan.FromSeconds(30);
        o.ClientTimeoutInterval = TimeSpan.FromSeconds(120);
    })
    .AddCircuitOptions(o =>
    {
        o.DetailedErrors = true;
    });


builder.Services.AddScoped(sp =>
{
    var nav = sp.GetRequiredService<NavigationManager>();
    return new HttpClient { BaseAddress = new Uri(nav.BaseUri) };
});


builder.Services.AddAntiforgery();

builder.Services.AddCascadingAuthenticationState();

builder.Services.AddControllers();


builder.Services.AddHttpContextAccessor();

builder.Services.AddWindowsService(options =>
{
    options.ServiceName = "VisitLog Service";
});

builder.Host.UseWindowsService();

builder.Services.AddSingleton<DatabaseFactory>();

builder.Services.AddDistributedMemoryCache();

builder.Services.AddSession(o => {
    o.Cookie.Name = ".VisitLog.Session";
    o.Cookie.HttpOnly = true;
    o.Cookie.SecurePolicy = CookieSecurePolicy.Always;
    o.Cookie.SameSite = SameSiteMode.Strict;
    o.Cookie.IsEssential = true;
    o.IdleTimeout = TimeSpan.FromMinutes(10);
});

builder.Logging.ClearProviders()
               .AddConsole();

#if WINDOWS
builder.Logging.AddEventLog();
#endif

var app = builder.Build();

if (!app.Environment.IsDevelopment())
{
    app.UseExceptionHandler("/Error", createScopeForErrors: true);
    app.UseHsts();
}

app.UseHttpsRedirection();

app.UseStaticFiles();

app.UseRouting();
app.UseSession();
app.UseAuthentication();
app.UseAuthorization(); 

app.MapRazorComponents<App>().AddInteractiveServerRenderMode();
app.MapIdentityEndpoints();
app.MapHub<VisitorLogHub>("/VisitorLogHub");

app.MapControllers();

app.UseAntiforgery();

app.MapGet("/start/{mode}", (string mode, HttpContext ctx) =>
{
    if (mode is not ("Entry" or "Departure" or "Recurring"))
        return Results.BadRequest("Invalid mode");

    ctx.Session.SetString("ScanMode", mode);

    // Decide where the browser should land next
    var target = mode switch
    {
        "Entry" => "/Identification",
        "Departure" => "/Scan",
        "Recurring" => "/Scan?mode=recurring",
        _ => "/"                      // fallback
    };

    return Results.Redirect(target);
})
.AllowAnonymous();           // the kiosk is public


app.Run();
