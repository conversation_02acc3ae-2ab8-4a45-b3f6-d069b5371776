﻿using Microsoft.AspNetCore.Mvc;
using ProtegeGXService;
using System.ServiceModel;
using System.Xml.Linq;
using VisitLog3.Helpers;
using VisitLog3.Models;

namespace VisitLog3.Controllers
{
    [Route("api/test")]
    [ApiController]
    public class GXController : ControllerBase
    {
        private readonly DatabaseFactory _dbFactory;
        public GXController(DatabaseFactory dbFactory)
        {
            _dbFactory = dbFactory;
        }

        [HttpGet]
        public void test()
        {
            var test = "test";
        }


    }
}
